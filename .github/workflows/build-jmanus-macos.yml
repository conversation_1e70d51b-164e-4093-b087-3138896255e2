# Copyright 2024-2026 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
name: Build JManus on MacOS

on:
  workflow_dispatch:
    inputs:
      build_version:
        description: 'Build version (optional, defaults to SNAPSHOT)'
        required: false
        default: '3.0.0-SNAPSHOT'
        type: string

jobs:
  build:
    name: Build JManus on ${{ matrix.platform }}
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        include:
          - os: macos-13
            platform: macos-x64
            artifact_name: jmanus-embedded-jdk-macos-x64
            arch_param: macos-x64
          - os: macos-latest
            platform: macos-arm
            artifact_name: jmanus-embedded-jdk-macos-arm
            arch_param: macos-aarch64

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'
          cache: maven

      - name: Cache Maven dependencies
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Build project with embedded JDK
        run: |
          chmod +x script/build_embedded_jdk.sh
          script/build_embedded_jdk.sh --arch ${{ matrix.arch_param }} --version "${{ github.event.inputs.build_version || '3.0.0-SNAPSHOT' }}"
        working-directory: spring-ai-alibaba-jmanus

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.artifact_name }}
          path: |
            spring-ai-alibaba-jmanus/dist/*.dmg
          retention-days: 30
