# Copyright 2024-2026 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
name: Greetings
on: [pull_request]
jobs:
  greeting:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: read
    steps:
      - uses: actions/first-interaction@v2.0.0
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          issue_message: |
            # Message with markdown.
            This is the message that will be displayed on users' first issue.
          pr_message: |-
            🎉 Congratulations, @${{ github.actor }}, on your inaugural contribution to the Spring AI Alibaba repository! Your efforts are sincerely appreciated.

            To maintain the integrity and legibility of our codebase, we kindly request that you verify your code adheres to the established project formatting standards prior to merging. Typically, comprehensive guidelines regarding code style and recommended formatting utilities can be found within the CONTRIBUTING.md file located in the repository.

            Should you encounter any queries or require clarification, please do not hesitate to raise them. We extend our gratitude once more for your valuable contribution!
