#
# Copyright 2024-2025 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: 🛠️ Build and Test
on:
  push:
    branches:
      - main
    paths-ignore:
      - '**.md'
  pull_request:
    branches:
      - main
    paths-ignore:
      - '**.md'
env:
  ENABLE_TEST_CI: true
permissions:
  contents: read
jobs:
  format:
    if: (github.repository == 'alibaba/spring-ai-alibaba')
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: ./tools/github-actions/setup-deps
      - run: make tools
      - run: make format-check
  check-style:
    if: (github.repository == 'alibaba/spring-ai-alibaba')
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: ./tools/github-actions/setup-deps
      - run: make tools
      - run: make checkstyle-check
  test:
    if: (github.repository == 'alibaba/spring-ai-alibaba')
    runs-on: ubuntu-22.04
    needs: [format, check-style]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: ./tools/github-actions/setup-deps
      - run: make tools
      - run: make test
  build:
    if: (github.repository == 'alibaba/spring-ai-alibaba')
    runs-on: ubuntu-22.04
    needs: [format, check-style, test]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: ./tools/github-actions/setup-deps
      - run: make tools
      - run: make build
