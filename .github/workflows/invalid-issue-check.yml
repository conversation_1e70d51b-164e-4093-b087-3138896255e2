# Copyright 2024-2026 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
name: "Close Invalid Issue"
on:
  schedule:
    - cron: "0 0,8,16 * * *"
permissions:
  contents: read
jobs:
  stale:
    permissions:
      issues: write
    runs-on: ubuntu-latest
    env:
      ACTIONS_STEP_DEBUG: true
    steps:
      - name: Close Stale Issues
        uses: actions/stale@v6
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: "This issue has been marked as invalid question, please give more information by following the `issue` template. The issue will be closed in 1 days if no further activity occurs."
          stale-issue-label: "stale"
          days-before-stale: 0
          days-before-close: 1
          remove-stale-when-updated: true
          only-labels: "invalid issue"
