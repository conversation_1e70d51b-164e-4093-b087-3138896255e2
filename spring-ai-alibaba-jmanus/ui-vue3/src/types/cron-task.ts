/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export interface CronConfig {
  id?: number | string
  cronName: string // 对应API返回的cronName字段
  cronTime: string // 对应API返回的cronTime字段（cron表达式）
  planDesc: string // 对应API返回的planDesc字段（任务描述）
  status: number // 0: 禁用, 1: 启用
  linkTemplate?: boolean // 是否关联计划模板
  templateId?: string // 关联的计划模板ID
  planTemplateId?: string // 后台接口使用的计划模板ID字段
  executionParams?: any // 执行参数
  createTime?: string
  updateTime?: string
}

export interface CronTaskListResponse {
  data: CronConfig[]
  total: number
}

