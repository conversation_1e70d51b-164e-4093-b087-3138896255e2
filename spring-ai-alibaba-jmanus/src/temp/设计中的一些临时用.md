
public class ChapterSplit extends InputSplit implements Writable {
    private Path file;
    private long start;
    private long length;
    private String chapterKey;
    private int partitionNum;
    private String[] hosts; // For data locality


这个结构可以作为固定输入



任务1：
    将所有的图片都识别为表格后汇总为一个大的表
    原来的流程：
        读取图片转换为markdown 
    MapReduce流程：
        切分Agent：读取文件并分配到不同的Agent目录里
        MapperAgent : 读取图片，都识别为markdown表格
        ReducerAgent : 总结合并
        


任务2：
    读取文件夹里的每个页面，从里面摘取出用户的社交网络账号，github账号等信息，放到一个excel表格里供我分析
    原来的流程：
        下载网页，把网页给llm，让他生成表格数据，然后合并所有的表格数据为一个整体表格
    MapReduce流程：
        下载足够多的网页
        任务分派Agent： 读取文件夹并分配到不同的子Agent里面
        MapperAgent: 从里面取出社交网络账号，github账号等信息，放到一个表格结构里
        ReducerAgent: 将所有的表格文件合并为一个表格


pattern :
    如果有输入的文件或目录
        自动切分为子任务
        执行流程
        合并任务
