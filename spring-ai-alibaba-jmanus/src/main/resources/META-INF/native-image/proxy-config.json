[{"interfaces": ["com.alibaba.cloud.ai.example.manus.config.ManusProperties", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.cglib.proxy.Factory"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.config.IConfigService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.cglib.proxy.Factory"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.config.ConfigService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.cglib.proxy.Factory"]}, {"interfaces": ["org.springframework.boot.context.properties.ConfigurationProperties", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.cglib.proxy.Factory"]}, {"interfaces": ["org.springframework.cglib.proxy.Dispatcher"]}, {"interfaces": ["org.springframework.cglib.proxy.MethodInterceptor"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.dynamic.agent.service.DynamicAgentLoader", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.cglib.proxy.Factory"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.llm.LlmService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.cglib.proxy.Factory"]}, {"interfaces": ["org.springframework.ai.model.tool.ToolCallingManager", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.cglib.proxy.Factory"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.planning.PlanningFactory", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.cglib.proxy.Factory"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.dynamic.agent.service.AgentService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.cglib.proxy.Factory"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.config.IConfigService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.config.repository.ConfigRepository", "org.springframework.data.repository.Repository", "org.springframework.transaction.interceptor.TransactionalProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.dynamic.agent.repository.DynamicAgentRepository", "org.springframework.data.repository.Repository", "org.springframework.transaction.interceptor.TransactionalProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.dynamic.agent.service.IDynamicAgentLoader", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.dynamic.mcp.repository.McpConfigRepository", "org.springframework.data.repository.Repository", "org.springframework.transaction.interceptor.TransactionalProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.dynamic.mcp.service.IMcpService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.dynamic.model.repository.DynamicModelRepository", "org.springframework.data.repository.Repository", "org.springframework.transaction.interceptor.TransactionalProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.dynamic.prompt.repository.PromptRepository", "org.springframework.data.repository.Repository", "org.springframework.transaction.interceptor.TransactionalProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.llm.ILlmService", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.planning.IPlanningFactory", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.planning.repository.PlanTemplateRepository", "org.springframework.data.repository.Repository", "org.springframework.transaction.interceptor.TransactionalProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.planning.repository.PlanTemplateVersionRepository", "org.springframework.data.repository.Repository", "org.springframework.transaction.interceptor.TransactionalProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["com.alibaba.cloud.ai.example.manus.recorder.repository.PlanExecutionRecordRepository", "org.springframework.data.repository.Repository", "org.springframework.transaction.interceptor.TransactionalProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["java.lang.reflect.GenericArrayType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.ParameterizedType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.TypeVariable", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.WildcardType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.sql.Connection"]}, {"interfaces": ["net.bytebuddy.description.method.MethodDescription$InDefinedShape$AbstractBase$Executable"]}, {"interfaces": ["net.bytebuddy.description.method.ParameterDescription$ForLoadedParameter$Parameter"]}, {"interfaces": ["net.bytebuddy.description.method.ParameterList$ForLoadedExecutable$Executable"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDefinition$Sort$AnnotatedType"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$ForLoadedType$Dispatcher"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic$AnnotationReader$Delegator$ForLoadedExecutableExceptionType$Dispatcher"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic$AnnotationReader$Delegator$ForLoadedExecutableParameterType$Dispatcher"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic$AnnotationReader$Delegator$ForLoadedMethodReturnType$Dispatcher"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic$AnnotationReader$ForComponentType$AnnotatedParameterizedType"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic$AnnotationReader$ForTypeArgument$AnnotatedParameterizedType"]}, {"interfaces": ["net.bytebuddy.dynamic.loading.ClassInjector$UsingLookup$MethodHandles"]}, {"interfaces": ["net.bytebuddy.dynamic.loading.ClassInjector$UsingLookup$MethodHandles$Lookup"]}, {"interfaces": ["org.hibernate.Session", "org.springframework.orm.jpa.EntityManagerProxy"]}, {"interfaces": ["org.hibernate.SessionFactory", "org.springframework.orm.jpa.EntityManagerFactoryInfo"]}, {"interfaces": ["org.hibernate.query.hql.spi.SqmQueryImplementor", "org.hibernate.query.sqm.internal.SqmInterpretationsKey$InterpretationsKeySource", "org.hibernate.query.spi.DomainQueryExecutionContext", "org.hibernate.query.SelectionQuery", "org.hibernate.query.CommonQueryContract"]}, {"interfaces": ["org.hibernate.query.sql.spi.NativeQueryImplementor", "org.hibernate.query.spi.DomainQueryExecutionContext", "org.hibernate.query.internal.ResultSetMappingResolutionContext", "org.hibernate.query.spi.QueryImplementor", "org.hibernate.query.SelectionQuery", "org.hibernate.query.CommonQueryContract"]}, {"interfaces": ["org.springframework.ai.model.tool.ToolCallingManager", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["org.springframework.boot.context.properties.ConfigurationProperties"]}, {"interfaces": ["org.springframework.data.jpa.repository.support.CrudMethodMetadata", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["org.springframework.web.bind.annotation.CrossOrigin"]}, {"interfaces": ["org.springframework.web.bind.annotation.PathVariable"]}, {"interfaces": ["org.springframework.web.bind.annotation.RequestMapping"]}]