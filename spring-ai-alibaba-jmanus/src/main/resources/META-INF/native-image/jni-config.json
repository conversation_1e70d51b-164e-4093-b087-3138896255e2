[{"name": "com.alibaba.cloud.ai.example.manus.OpenManusSpringBootApplication", "methods": [{"name": "main", "parameterTypes": ["java.lang.String[]"]}]}, {"name": "java.lang.Bo<PERSON>an", "methods": [{"name": "getBoolean", "parameterTypes": ["java.lang.String"]}]}, {"name": "java.lang.String", "methods": [{"name": "lastIndexOf", "parameterTypes": ["int"]}, {"name": "substring", "parameterTypes": ["int"]}]}, {"name": "java.lang.System", "methods": [{"name": "getProperty", "parameterTypes": ["java.lang.String"]}, {"name": "load", "parameterTypes": ["java.lang.String"]}, {"name": "setProperty", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"name": "sun.lwawt.macosx.LWCToolkit", "methods": [{"name": "installToolkitThreadInJava", "parameterTypes": []}]}, {"name": "sun.management.VMManagementImpl", "fields": [{"name": "compTimeMonitoringSupport"}, {"name": "currentThreadCpuTimeSupport"}, {"name": "objectMonitorUsageSupport"}, {"name": "otherThreadCpuTimeSupport"}, {"name": "remoteDiagnosticCommandsSupport"}, {"name": "synchronizerUsageSupport"}, {"name": "threadAllocatedMemorySupport"}, {"name": "threadContentionMonitoringSupport"}]}]