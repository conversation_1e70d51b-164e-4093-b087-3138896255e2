{"resources": {"includes": [{"pattern": "\\QMETA-INF/spring.factories\\E"}, {"pattern": "\\QMETA-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports\\E"}, {"pattern": "\\QMETA-INF/spring/org.springframework.boot.actuate.autoconfigure.web.ManagementContextConfiguration.imports\\E"}, {"pattern": "\\Qapplication.yml\\E"}, {"pattern": "\\Qapplication.yaml\\E"}, {"pattern": "\\Qapplication.properties\\E"}, {"pattern": "\\Qapplication-*.yml\\E"}, {"pattern": "\\Qapplication-*.yaml\\E"}, {"pattern": "\\Qapplication-*.properties\\E"}, {"pattern": "\\Qlog4j2.xml\\E"}, {"pattern": "\\Qlogback-spring.xml\\E"}, {"pattern": "\\Qlogback.xml\\E"}, {"pattern": "\\Qstatic/.*\\E"}, {"pattern": "\\Qprompts/.*\\E"}, {"pattern": "\\Qprompts/agent/.*\\E"}, {"pattern": "\\Qprompts/browser-agent/.*\\E"}, {"pattern": "\\Qprompts/llm/.*\\E"}, {"pattern": "\\Qprompts/planning/.*\\E"}, {"pattern": "\\Qprompts/startup-agents/.*\\E"}, {"pattern": "\\Qtemplates/.*\\E"}, {"pattern": "\\Qpublic/.*\\E"}, {"pattern": "\\Q.properties\\E"}, {"pattern": "\\Q.xml\\E"}, {"pattern": "\\Q.json\\E"}, {"pattern": "\\Q.yml\\E"}, {"pattern": "\\Q.yaml\\E"}]}, "bundles": []}