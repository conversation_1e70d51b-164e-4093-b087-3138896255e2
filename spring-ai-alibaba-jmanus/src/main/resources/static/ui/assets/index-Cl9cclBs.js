import{d as L,u as E,r as p,c as v,Q as $,o as B,A as I,a as r,b as l,e as t,f as g,g as h,x as u,t as d,h as x,F as D,l as F,n as N,s as w,m as U}from"./index-BaYfAMDB.js";import{I as i}from"./iconify-Cd9Ozm-0.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";const M={class:"language-switcher"},O=["title"],S={class:"current-lang"},z={class:"dropdown-header"},A={class:"language-options"},K=["disabled","onClick"],Q={class:"lang-code"},j={class:"lang-name"},q=L({__name:"index",setup(G){const{locale:_}=E(),a=p(!1),o=v(()=>_.value),f=v(()=>$.opts),b=v(()=>{const e=f.value.find(n=>n.value===o.value);return e?e.title:"Unknown"}),y=()=>{a.value=!a.value},c=p(!1),C=async e=>{if(!(c.value||o.value===e))try{c.value=!0,await U(e),a.value=!1}catch(n){console.error("Failed to change language:",n),a.value=!1}finally{c.value=!1}},k=e=>{e.target.closest(".language-switcher")||(a.value=!1)},m=e=>{e.key==="Escape"&&(a.value=!1)};return B(()=>{document.addEventListener("click",k),document.addEventListener("keydown",m)}),I(()=>{document.removeEventListener("click",k),document.removeEventListener("keydown",m)}),(e,n)=>(l(),r("div",M,[t("button",{class:"language-btn",onClick:y,title:e.$t("language.switch")},[h(u(i),{icon:"carbon:translate",width:"18"}),t("span",S,d(b.value),1),h(u(i),{icon:a.value?"carbon:chevron-up":"carbon:chevron-down",width:"14",class:"chevron"},null,8,["icon"])],8,O),a.value?(l(),r("div",{key:0,class:"language-dropdown",onClick:n[1]||(n[1]=x(()=>{},["stop"]))},[t("div",z,[t("span",null,d(e.$t("language.switch")),1),t("button",{class:"close-btn",onClick:n[0]||(n[0]=s=>a.value=!1)},[h(u(i),{icon:"carbon:close",width:"16"})])]),t("div",A,[(l(!0),r(D,null,F(f.value,s=>(l(),r("button",{key:s.value,class:N(["language-option",{active:o.value===s.value,loading:c.value&&o.value!==s.value}]),disabled:c.value,onClick:H=>C(s.value)},[t("span",Q,d(s.value.toUpperCase()),1),t("span",j,d(s.title),1),c.value&&o.value!==s.value?(l(),w(u(i),{key:0,icon:"carbon:circle-dash",width:"16",class:"loading-icon"})):o.value===s.value?(l(),w(u(i),{key:1,icon:"carbon:checkmark",width:"16",class:"check-icon"})):g("",!0)],10,K))),128))])])):g("",!0),a.value?(l(),r("div",{key:1,class:"backdrop",onClick:n[2]||(n[2]=s=>a.value=!1)})):g("",!0)]))}}),T=V(q,[["__scopeId","data-v-8dd61fe2"]]);export{T as L};
