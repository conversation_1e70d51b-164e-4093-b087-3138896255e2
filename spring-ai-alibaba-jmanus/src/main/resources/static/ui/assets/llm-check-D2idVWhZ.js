var o=Object.defineProperty;var l=(e,t,i)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i;var c=(e,t,i)=>l(e,typeof t!="symbol"?t+"":t,i);class r{static async checkLlmConfiguration(){const t=Date.now();if(this.cachedStatus&&t-this.cachedStatus.lastCheck<this.CACHE_DURATION)return{initialized:this.cachedStatus.initialized};try{const i=await fetch("/api/init/status");if(!i.ok)throw new Error(`检查失败: ${i.status}`);const s=await i.json(),a=s.success&&s.initialized;return this.cachedStatus={initialized:a,lastCheck:t},a?{initialized:!0}:{initialized:!1,message:"系统尚未配置LLM模型，请先通过初始化页面配置API密钥。"}}catch(i){return console.error("[LlmCheckService] 检查LLM配置失败:",i),{initialized:!1,message:"无法检查LLM配置状态，请确保系统正常运行。"}}}static async ensureLlmConfigured(t){const{showAlert:i=!0,redirectToInit:s=!0}=t||{},a=await this.checkLlmConfiguration();if(!a.initialized){const n=a.message||"请先配置LLM模型";throw i&&alert(n),s?(localStorage.removeItem("hasInitialized"),window.location.href="/ui/#/init",new Error("Redirecting to initialization page")):new Error(n)}}static clearCache(){this.cachedStatus=null}static async withLlmCheck(t,i){return await this.ensureLlmConfigured(i),t()}}c(r,"cachedStatus",null),c(r,"CACHE_DURATION",3e4);export{r as L};
