var it=Object.defineProperty;var ct=(w,o,a)=>o in w?it(w,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):w[o]=a;var he=(w,o,a)=>ct(w,typeof o!="symbol"?o+"":o,a);import{d as Pe,u as Te,c as ke,o as Ce,a as g,b as h,n as te,x as l,e,f as B,t as i,g as k,i as Y,F as ge,l as fe,h as ie,w as re,j as be,z as ot,r as R,y as ne,A as Ie,s as ce,T as De,k as xe,B as _e,q as Ne,C as Ae,D as rt,E as ut,p as at,G as dt}from"./index-BaYfAMDB.js";import{I as $}from"./iconify-Cd9Ozm-0.js";import{s as f,P as Me,u as lt}from"./sidebar-WUCD0_g3.js";import{_ as Se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{L as pt}from"./llm-check-D2idVWhZ.js";import{L as ht}from"./index-Cl9cclBs.js";import{u as gt}from"./useToast-DCEgljFh.js";const mt={class:"sidebar-content"},vt={class:"sidebar-content-header"},ft={class:"sidebar-content-title"},bt={class:"tab-switcher"},kt=["disabled"],_t={key:0,class:"tab-content"},$t={class:"new-task-section"},Pt={class:"sidebar-content-list"},Ct={key:0,class:"loading-state"},St={key:1,class:"error-state"},yt={key:2,class:"empty-state"},Et=["onClick"],wt={class:"task-icon"},Tt={class:"task-details"},It={class:"task-title"},Dt={class:"task-preview"},xt={class:"task-time"},Rt={class:"task-actions"},At=["title","onClick"],Mt={key:1,class:"tab-content config-tab"},Nt={key:0,class:"config-container"},Ut={class:"template-info-header"},Lt={class:"template-info"},Vt={class:"template-id"},qt={class:"config-section"},Ft={class:"section-header"},Ot={class:"generator-content"},Bt=["placeholder"],Wt={class:"generator-actions"},jt=["disabled"],Ht=["disabled"],Jt={class:"config-section"},zt={class:"section-header"},Gt={class:"section-actions"},Xt=["disabled","title"],Kt=["disabled","title"],Qt=["disabled"],Yt=["placeholder"],Zt={class:"config-section"},en={class:"section-header"},tn={class:"execution-content"},nn={class:"params-input-group"},sn={class:"params-help-text"},on={class:"params-input-container"},an=["placeholder"],ln=["title"],cn={class:"api-url-display"},rn={class:"api-url-label"},un={class:"api-url"},dn={class:"api-url-display"},pn={class:"api-url-label"},hn=["disabled"],gn=Pe({__name:"index",emits:["planExecutionRequested"],setup(w,{expose:o,emit:a}){const{t:r}=Te(),T=["currentPlanId","userRequest","rootPlanId"],v=ke({get(){try{if(!f.jsonContent)return"";const d={...JSON.parse(f.jsonContent)};return T.forEach(E=>{delete d[E]}),JSON.stringify(d,null,2)}catch{return f.jsonContent}},set(s){try{if(!s.trim()){f.jsonContent="";return}const d=JSON.parse(s);let E={};try{E=JSON.parse(f.jsonContent||"{}")}catch{}const X={...d};T.forEach(L=>{E[L]!==void 0&&(X[L]=E[L])}),f.jsonContent=JSON.stringify(X)}catch{f.jsonContent=s}}}),_=a,x=async()=>{try{const s=await f.saveTemplate();s!=null&&s.duplicate?alert(r("sidebar.saveCompleted",{message:s.message,versionCount:s.versionCount})):s!=null&&s.saved?alert(r("sidebar.saveSuccess",{message:s.message,versionCount:s.versionCount})):s!=null&&s.message&&alert(r("sidebar.saveStatus",{message:s.message}))}catch(s){console.error("保存计划修改失败:",s),alert(s.message||r("sidebar.saveFailed"))}},I=async()=>{var s;try{await f.generatePlan(),alert(r("sidebar.generateSuccess",{templateId:((s=f.selectedTemplate)==null?void 0:s.id)??r("sidebar.unknown")}))}catch(d){console.error("生成计划失败:",d),alert(r("sidebar.generateFailed")+": "+d.message)}},D=async()=>{try{await f.updatePlan(),alert(r("sidebar.updateSuccess"))}catch(s){console.error("更新计划失败:",s),alert(r("sidebar.updateFailed")+": "+s.message)}},K=async()=>{console.log("[Sidebar] handleExecutePlan called");try{const s=f.preparePlanExecution();if(!s){console.log("[Sidebar] No plan data available, returning");return}console.log("[Sidebar] 触发计划执行请求:",s),console.log("[Sidebar] Emitting planExecutionRequested event"),_("planExecutionRequested",s),console.log("[Sidebar] Event emitted")}catch(s){console.error("执行计划出错:",s),alert(r("sidebar.executeFailed")+": "+s.message)}finally{f.finishPlanExecution()}},G=s=>{if(isNaN(s.getTime()))return console.warn("Invalid date received:",s),r("time.unknown");const E=new Date().getTime()-s.getTime(),X=Math.floor(E/6e4),L=Math.floor(E/36e5),O=Math.floor(E/864e5);return X<1?r("time.now"):X<60?r("time.minuteAgo",{count:X}):L<24?r("time.hourAgo",{count:L}):O<30?r("time.dayAgo",{count:O}):s.toLocaleDateString("zh-CN")},V=(s,d)=>!s||s.length<=d?s:s.substring(0,d)+"...";return Ce(()=>{f.loadPlanTemplateList()}),o({loadPlanTemplateList:f.loadPlanTemplateList,toggleSidebar:f.toggleSidebar,currentPlanTemplateId:f.currentPlanTemplateId}),(s,d)=>(h(),g("div",{class:te(["sidebar-wrapper",{"sidebar-wrapper-collapsed":l(f).isCollapsed}])},[e("div",mt,[e("div",vt,[e("div",ft,i(s.$t("sidebar.title")),1)]),e("div",bt,[e("button",{class:te(["tab-button",{active:l(f).currentTab==="list"}]),onClick:d[0]||(d[0]=E=>l(f).switchToTab("list"))},[k(l($),{icon:"carbon:list",width:"16"}),Y(" "+i(s.$t("sidebar.templateList")),1)],2),e("button",{class:te(["tab-button",{active:l(f).currentTab==="config"}]),onClick:d[1]||(d[1]=E=>l(f).switchToTab("config")),disabled:!l(f).selectedTemplate},[k(l($),{icon:"carbon:settings",width:"16"}),Y(" "+i(s.$t("sidebar.configuration")),1)],10,kt)]),l(f).currentTab==="list"?(h(),g("div",_t,[e("div",$t,[e("button",{class:"new-task-btn",onClick:d[2]||(d[2]=E=>l(f).createNewTemplate())},[k(l($),{icon:"carbon:add",width:"16"}),Y(" "+i(s.$t("sidebar.newPlan"))+" ",1),d[11]||(d[11]=e("span",{class:"shortcut"},"⌘ K",-1))])]),e("div",Pt,[l(f).isLoading?(h(),g("div",Ct,[k(l($),{icon:"carbon:circle-dash",width:"20",class:"spinning"}),e("span",null,i(s.$t("sidebar.loading")),1)])):l(f).errorMessage?(h(),g("div",St,[k(l($),{icon:"carbon:warning",width:"20"}),e("span",null,i(l(f).errorMessage),1),e("button",{onClick:d[3]||(d[3]=(...E)=>l(f).loadPlanTemplateList&&l(f).loadPlanTemplateList(...E)),class:"retry-btn"},i(s.$t("sidebar.retry")),1)])):l(f).planTemplateList.length===0?(h(),g("div",yt,[k(l($),{icon:"carbon:document",width:"32"}),e("span",null,i(s.$t("sidebar.noTemplates")),1)])):(h(!0),g(ge,{key:3},fe(l(f).sortedTemplates,E=>(h(),g("div",{key:E.id,class:te(["sidebar-content-list-item",{"sidebar-content-list-item-active":E.id===l(f).currentPlanTemplateId}]),onClick:X=>l(f).selectTemplate(E)},[e("div",wt,[k(l($),{icon:"carbon:document",width:"20"})]),e("div",Tt,[e("div",It,i(E.title||s.$t("sidebar.unnamedPlan")),1),e("div",Dt,i(V(E.description||s.$t("sidebar.noDescription"),40)),1)]),e("div",xt,i(G(l(f).parseDateTime(E.updateTime||E.createTime))),1),e("div",Rt,[e("button",{class:"delete-task-btn",title:s.$t("sidebar.deleteTemplate"),onClick:ie(X=>l(f).deleteTemplate(E),["stop"])},[k(l($),{icon:"carbon:close",width:"16"})],8,At)])],10,Et))),128))])])):l(f).currentTab==="config"?(h(),g("div",Mt,[l(f).selectedTemplate?(h(),g("div",Nt,[e("div",Ut,[e("div",Lt,[e("h3",null,i(l(f).selectedTemplate.title||s.$t("sidebar.unnamedPlan")),1),e("span",Vt,"ID: "+i(l(f).selectedTemplate.id),1)]),e("button",{class:"back-to-list-btn",onClick:d[4]||(d[4]=E=>l(f).switchToTab("list"))},[k(l($),{icon:"carbon:arrow-left",width:"16"})])]),e("div",qt,[e("div",Ft,[k(l($),{icon:"carbon:generate",width:"16"}),e("span",null,i(s.$t("sidebar.planGenerator")),1)]),e("div",Ot,[re(e("textarea",{"onUpdate:modelValue":d[5]||(d[5]=E=>l(f).generatorPrompt=E),class:"prompt-input",placeholder:s.$t("sidebar.generatorPlaceholder"),rows:"3"},null,8,Bt),[[be,l(f).generatorPrompt]]),e("div",Wt,[e("button",{class:"btn btn-primary btn-sm",onClick:I,disabled:l(f).isGenerating||!l(f).generatorPrompt.trim()},[k(l($),{icon:l(f).isGenerating?"carbon:circle-dash":"carbon:generate",width:"14",class:te({spinning:l(f).isGenerating})},null,8,["icon","class"]),Y(" "+i(l(f).isGenerating?s.$t("sidebar.generating"):s.$t("sidebar.generatePlan")),1)],8,jt),e("button",{class:"btn btn-secondary btn-sm",onClick:D,disabled:l(f).isGenerating||!l(f).generatorPrompt.trim()||!l(f).jsonContent.trim()},[k(l($),{icon:"carbon:edit",width:"14"}),Y(" "+i(s.$t("sidebar.updatePlan")),1)],8,Ht)])])]),e("div",Jt,[e("div",zt,[k(l($),{icon:"carbon:code",width:"16"}),e("span",null,i(s.$t("sidebar.jsonTemplate")),1),e("div",Gt,[e("button",{class:"btn btn-sm",onClick:d[6]||(d[6]=(...E)=>l(f).rollbackVersion&&l(f).rollbackVersion(...E)),disabled:!l(f).canRollback,title:s.$t("sidebar.rollback")},[k(l($),{icon:"carbon:undo",width:"14"})],8,Xt),e("button",{class:"btn btn-sm",onClick:d[7]||(d[7]=(...E)=>l(f).restoreVersion&&l(f).restoreVersion(...E)),disabled:!l(f).canRestore,title:s.$t("sidebar.restore")},[k(l($),{icon:"carbon:redo",width:"14"})],8,Kt),e("button",{class:"btn btn-primary btn-sm",onClick:x,disabled:l(f).isGenerating||l(f).isExecuting},[k(l($),{icon:"carbon:save",width:"14"})],8,Qt)])]),re(e("textarea",{"onUpdate:modelValue":d[8]||(d[8]=E=>v.value=E),class:"json-editor",placeholder:s.$t("sidebar.jsonPlaceholder"),rows:"12"},null,8,Yt),[[be,v.value]])]),e("div",Zt,[e("div",en,[k(l($),{icon:"carbon:play",width:"16"}),e("span",null,i(s.$t("sidebar.executionController")),1)]),e("div",tn,[e("div",nn,[e("label",null,i(s.$t("sidebar.executionParams")),1),e("div",sn,i(s.$t("sidebar.executionParamsHelp")),1),e("div",on,[re(e("input",{"onUpdate:modelValue":d[9]||(d[9]=E=>l(f).executionParams=E),class:"params-input",placeholder:s.$t("sidebar.executionParamsPlaceholder")},null,8,an),[[be,l(f).executionParams]]),e("button",{class:"clear-params-btn",onClick:d[10]||(d[10]=(...E)=>l(f).clearExecutionParams&&l(f).clearExecutionParams(...E)),title:s.$t("sidebar.clearParams")},[k(l($),{icon:"carbon:close",width:"12"})],8,ln)])]),e("div",cn,[e("span",rn,i(s.$t("sidebar.apiUrl"))+":",1),e("code",un,i(l(f).computedApiUrl),1)]),e("div",dn,[e("span",pn,i(s.$t("sidebar.statusApiUrl"))+":",1),d[12]||(d[12]=e("code",{class:"api-url"},"/api/executor/details/{planId}",-1))]),e("button",{class:"btn btn-primary execute-btn",onClick:K,disabled:l(f).isExecuting||l(f).isGenerating},[k(l($),{icon:l(f).isExecuting?"carbon:circle-dash":"carbon:play",width:"16",class:te({spinning:l(f).isExecuting})},null,8,["icon","class"]),Y(" "+i(l(f).isExecuting?s.$t("sidebar.executing"):s.$t("sidebar.executePlan")),1)],8,hn)])])])):B("",!0)])):B("",!0)])],2))}}),mn=Se(gn,[["__scopeId","data-v-6a8987dc"]]);class Le{static async sendMessage(o){return pt.withLlmCheck(async()=>{const a=await fetch(`${this.BASE_URL}/execute`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:o})});if(!a.ok)throw new Error(`API request failed: ${a.status}`);return await a.json()})}}he(Le,"BASE_URL","/api/executor");class Ve{static async getDetails(o){try{const a=await fetch(`${this.BASE_URL}/details/${o}`);if(a.status===404)return null;if(!a.ok)throw new Error(`Failed to get detailed information: ${a.status}`);const r=await a.text(),T=JSON.parse(r);return T&&typeof T=="object"&&!T.currentPlanId&&(T.currentPlanId=o),T}catch(a){return console.error("[CommonApiService] Failed to get plan details:",a),null}}static async submitFormInput(o,a){const r=await fetch(`${this.BASE_URL}/submit-input/${o}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok){let v;try{v=await r.json()}catch{v={message:`Failed to submit form input: ${r.status}`}}throw new Error(v.message||`Failed to submit form input: ${r.status}`)}const T=r.headers.get("content-type");return T&&T.indexOf("application/json")!==-1?await r.json():{success:!0}}static async getAllPrompts(){try{const o=await fetch(this.BASE_URL);return await(await this.handleResponse(o)).json()}catch(o){throw console.error("Failed to get Prompt list:",o),o}}static async handleResponse(o){if(!o.ok)try{const a=await o.json();throw new Error(a.message||`API request failed: ${o.status}`)}catch{throw new Error(`API request failed: ${o.status} ${o.statusText}`)}return o}}he(Ve,"BASE_URL","/api/executor");const $e=class $e{constructor(){he(this,"POLL_INTERVAL",5e3);he(this,"state",ot({activePlanId:null,lastSequenceSize:0,isPolling:!1,pollTimer:null}));he(this,"callbacks",{});he(this,"planExecutionCache",new Map);he(this,"uiStateCache",new Map);console.log("[PlanExecutionManager] Initialized with callback-based event system")}getCachedPlanRecord(o){return this.planExecutionCache.get(o)}getCachedUIState(o){return this.uiStateCache.get(o)}setCachedUIState(o,a){this.uiStateCache.set(o,a),console.log(`[PlanExecutionManager] Cached UI state for rootPlanId: ${o}`)}getAllCachedRecords(){return new Map(this.planExecutionCache)}hasCachedPlanRecord(o){return this.planExecutionCache.has(o)}setCachedPlanRecord(o,a){this.planExecutionCache.set(o,a),console.log(`[PlanExecutionManager] Cached plan execution record for rootPlanId: ${o}`)}clearCachedPlanRecord(o){const a=this.planExecutionCache.delete(o);return a&&console.log(`[PlanExecutionManager] Cleared cached plan execution record for rootPlanId: ${o}`),a}clearAllCachedRecords(){const o=this.planExecutionCache.size,a=this.uiStateCache.size;this.planExecutionCache.clear(),this.uiStateCache.clear(),console.log(`[PlanExecutionManager] Cleared all caches - Plans: ${o}, UI States: ${a}`)}static getInstance(){return $e.instance||($e.instance=new $e),$e.instance}getActivePlanId(){return this.state.activePlanId}getState(){return this.state}setEventCallbacks(o){this.callbacks={...this.callbacks,...o},console.log("[PlanExecutionManager] Event callbacks set:",Object.keys(o))}async handleUserMessageSendRequested(o){if(this.validateAndPrepareUIForNewRequest(o))try{if(await this.sendUserMessageAndSetPlanId(o),this.state.activePlanId)this.initiatePlanExecutionSequence(o,this.state.activePlanId);else throw new Error("Failed to get valid plan ID")}catch(a){console.error("[PlanExecutionManager] Failed to send user message:",a);const r=this.state.activePlanId??"error";this.setCachedUIState(r,{enabled:!0}),this.emitChatInputUpdateState(r),this.state.activePlanId=null}}handlePlanExecutionRequested(o,a){console.log("[PlanExecutionManager] Received plan execution request:",{planId:o,query:a}),o?(this.state.activePlanId=o,this.initiatePlanExecutionSequence(a??"执行计划",o)):console.error("[PlanExecutionManager] Invalid plan execution request: missing planId")}handleCachedPlanExecution(o,a){const r=this.getCachedPlanRecord(o);return r!=null&&r.currentPlanId?(console.log(`[PlanExecutionManager] Found cached plan execution record for rootPlanId: ${o}`),this.handlePlanExecutionRequested(r.currentPlanId,a),!0):(console.log(`[PlanExecutionManager] No cached plan execution record found for rootPlanId: ${o}`),!1)}validateAndPrepareUIForNewRequest(o){if(!o)return console.warn("[PlanExecutionManager] Query is empty"),!1;if(this.state.activePlanId)return!1;this.emitChatInputClear();const a=this.state.activePlanId??"ui-state";return this.setCachedUIState(a,{enabled:!1,placeholder:"Processing..."}),this.emitChatInputUpdateState(a),!0}async sendUserMessageAndSetPlanId(o){try{const a=await Le.sendMessage(o);if(a!=null&&a.planId)return this.state.activePlanId=a.planId,a;if(a!=null&&a.planTemplateId)return this.state.activePlanId=a.planTemplateId,{...a,planId:a.planTemplateId};throw console.error("[PlanExecutionManager] Failed to get planId from response:",a),new Error("Failed to get valid planId from API response")}catch(a){throw console.error("[PlanExecutionManager] API call failed:",a),a}}initiatePlanExecutionSequence(o,a){console.log(`[PlanExecutionManager] Starting plan execution sequence for query: "${o}", planId: ${a}`);const r=a;this.emitDialogRoundStart(r),this.startPolling()}handlePlanCompletion(o){this.emitPlanCompleted(o.rootPlanId??""),this.state.lastSequenceSize=0,this.stopPolling();try{setTimeout(async()=>{if(this.state.activePlanId)try{await Me.deletePlanTemplate(this.state.activePlanId),console.log(`[PlanExecutionManager] Plan template ${this.state.activePlanId} deleted successfully`)}catch(a){console.log(`Delete plan execution record failed: ${a.message}`)}},5e3)}catch(a){console.log(`Delete plan execution record failed: ${a.message}`)}o.completed&&(this.state.activePlanId=null,this.emitChatInputUpdateState(o.rootPlanId??""))}async pollPlanStatus(){if(this.state.activePlanId){if(this.state.isPolling){console.log("[PlanExecutionManager] Previous polling still in progress, skipping");return}try{this.state.isPolling=!0;const o=await this.getPlanDetails(this.state.activePlanId);if(!o){console.warn("[PlanExecutionManager] No details received from API");return}if(o.rootPlanId&&this.setCachedPlanRecord(o.rootPlanId,o),!o.steps||o.steps.length===0){console.log("[PlanExecutionManager] Simple response without steps detected, handling as completed"),this.emitPlanUpdate(o.rootPlanId??""),this.handlePlanCompletion(o);return}this.emitPlanUpdate(o.rootPlanId??""),o.completed&&this.handlePlanCompletion(o)}catch(o){console.error("[PlanExecutionManager] Failed to poll plan status:",o)}finally{this.state.isPolling=!1}}}async getPlanDetails(o){try{const a=await Ve.getDetails(o);return a!=null&&a.rootPlanId&&(this.planExecutionCache.set(a.rootPlanId,a),console.log(`[PlanExecutionManager] Cached plan execution record for rootPlanId: ${a.rootPlanId}`)),a}catch(a){return console.error("[PlanExecutionManager] Failed to get plan details:",a),null}}startPolling(){this.state.pollTimer&&clearInterval(this.state.pollTimer),this.state.pollTimer=window.setInterval(()=>{this.pollPlanStatus()},this.POLL_INTERVAL),console.log("[PlanExecutionManager] Started polling")}async pollPlanStatusImmediately(){console.log("[PlanExecutionManager] Polling plan status immediately"),await this.pollPlanStatus()}stopPolling(){this.state.pollTimer&&(clearInterval(this.state.pollTimer),this.state.pollTimer=null),console.log("[PlanExecutionManager] Stopped polling")}cleanup(){this.stopPolling(),this.state.activePlanId=null,this.state.lastSequenceSize=0,this.state.isPolling=!1,this.clearAllCachedRecords()}emitChatInputClear(){this.callbacks.onChatInputClear&&this.callbacks.onChatInputClear()}emitChatInputUpdateState(o){this.callbacks.onChatInputUpdateState&&this.callbacks.onChatInputUpdateState(o)}emitDialogRoundStart(o){this.callbacks.onDialogRoundStart&&this.callbacks.onDialogRoundStart(o)}emitPlanUpdate(o){this.callbacks.onPlanUpdate&&this.callbacks.onPlanUpdate(o)}emitPlanCompleted(o){this.callbacks.onPlanCompleted&&this.callbacks.onPlanCompleted(o)}};he($e,"instance",null);let Ue=$e;const oe=Ue.getInstance(),vn={class:"right-panel"},fn={class:"preview-header"},bn={class:"preview-tabs"},kn={class:"tab-button active"},_n={class:"preview-content"},$n={class:"step-details"},Pn={key:0,class:"step-info-fixed"},Cn={key:0,class:"agent-info"},Sn={class:"info-item"},yn={class:"label"},En={class:"value"},wn={class:"info-item"},Tn={class:"label"},In={class:"value"},Dn={class:"info-item"},xn={class:"label"},Rn={class:"value"},An={class:"info-item"},Mn={class:"label"},Nn={class:"value"},Un={class:"info-item"},Ln={class:"label"},Vn={class:"execution-status"},qn={class:"status-item"},Fn={class:"status-text"},On={key:0},Bn={key:0,class:"think-act-steps"},Wn={class:"steps-container"},jn={class:"step-header"},Hn={class:"step-number"},Jn={class:"think-section"},zn={class:"think-content"},Gn={class:"input"},Xn={class:"label"},Kn={class:"output"},Qn={class:"label"},Yn={key:0,class:"action-section"},Zn={class:"action-content"},es={class:"tool-info"},ts={class:"label"},ns={class:"value"},ss={class:"input"},os={class:"label"},as={class:"output"},ls={class:"label"},is={key:0,class:"sub-plan-section"},cs={class:"sub-plan-content"},rs={class:"sub-plan-header"},us={class:"sub-plan-info"},ds={class:"value"},ps={key:0,class:"sub-plan-info"},hs={class:"value"},gs={class:"sub-plan-status"},ms={class:"status-text"},vs={key:0,class:"no-steps-message"},fs={key:1,class:"no-execution-message"},bs={class:"step-basic-info"},ks={class:"info-item"},_s={class:"label"},$s={class:"value"},Ps={key:0,class:"info-item"},Cs={class:"value"},Ss={class:"info-item"},ys={class:"no-execution-hint"},Es={key:2,class:"execution-indicator"},ws={class:"execution-text"},Ts={key:1,class:"no-selection"},Is=["title"],Ds=Pe({__name:"index",setup(w,{expose:o}){const{t:a}=Te(),r=R(),T=R(),v=R(),_=R(null),x=R(!1),I=R(!0),D=R(!0),K=ke(()=>v.value?v.value.completed?a("rightPanel.status.completed"):v.value.current?a("rightPanel.status.executing"):a("rightPanel.status.waiting"):""),G=C=>{var F;if(console.log(`[RightPanel] updateDisplayedPlanProgress called with rootPlanId: ${C}`),v.value&&_.value){const A=_.value.rootPlanId??T.value;if(A&&A!==C){console.log(`[RightPanel] Plan ID mismatch - skipping update. Current: ${A}, Requested: ${C}`);return}}console.log(`[RightPanel] Plan ID validation passed - proceeding with update for rootPlanId: ${C}`);const P=oe.getCachedPlanRecord(C);if(!P){console.warn(`[RightPanel] Plan data not found for rootPlanId: ${C}`);return}if(P.steps&&P.steps.length>0){const A=P.steps.length,y=(P.currentStepIndex??0)+1;console.log(`[RightPanel] Progress: ${y} / ${A}`)}if(v.value&&T.value&&(T.value===C||((F=_.value)==null?void 0:F.rootPlanId)===C)&&(console.log(`[RightPanel] Refreshing selected step details for plan: ${C}`),_.value)){const y=_.value,M=s(y.planId,y.rootPlanId,y.subPlanId);M?(d(M,y.stepIndex,y.planId,y.isSubPlan),q()):console.warn("[RightPanel] Could not find plan record for refresh:",y)}},V=(C,P,F,A,y)=>{console.log("[RightPanel] Step selected:",{planId:C,stepIndex:P,rootPlanId:F,subPlanId:A,subStepIndex:y});const M=!!(F&&A&&y!==void 0);_.value={planId:C,stepIndex:P,isSubPlan:M,...M&&{rootPlanId:F,subPlanId:A,subStepIndex:y}};const ee=s(C,F,A);if(!ee){console.warn("[RightPanel] Plan data not found:",{planId:C,rootPlanId:F,subPlanId:A}),v.value=null,_.value=null;return}d(ee,P,C,M)},s=(C,P,F)=>{var M;if(!P||!F)return oe.getCachedPlanRecord(C)??null;const A=oe.getCachedPlanRecord(C);if(A)return A;const y=oe.getCachedPlanRecord(P);if(!(y!=null&&y.agentExecutionSequence))return null;for(const ee of y.agentExecutionSequence)if(ee.thinkActSteps){for(const se of ee.thinkActSteps)if(((M=se.subPlanExecutionRecord)==null?void 0:M.currentPlanId)===F)return se.subPlanExecutionRecord}return null},d=(C,P,F,A)=>{var de,b,m,u,S;if(!C.steps||P>=C.steps.length){v.value=null,_.value=null,console.warn("[RightPanel] Invalid step data:",{planId:F,stepIndex:P,hasSteps:!!C.steps,stepsLength:(de=C.steps)==null?void 0:de.length,message:"Invalid step index"});return}T.value=F;const y=C.steps[P],M=(b=C.agentExecutionSequence)==null?void 0:b[P];console.log("[RightPanel] Step data details:",{planId:F,stepIndex:P,step:y,hasAgentExecutionSequence:!!C.agentExecutionSequence,agentExecutionSequenceLength:(m=C.agentExecutionSequence)==null?void 0:m.length,agentExecution:M,hasThinkActSteps:!!(M!=null&&M.thinkActSteps),thinkActStepsLength:(u=M==null?void 0:M.thinkActSteps)==null?void 0:u.length,isSubPlan:A});const ee=(M==null?void 0:M.status)==="FINISHED",se=!ee&&P===C.currentStepIndex&&!C.completed,ve={planId:F,index:P,title:typeof y=="string"?y:y.title||y.description||y.name||`${A?"子":""}步骤 ${P+1}`,description:typeof y=="string"?y:y.description||y,completed:ee,current:se};M&&(ve.agentExecution=M),v.value=ve,console.log("[RightPanel] Step details updated:",{planId:F,stepIndex:P,stepTitle:v.value.title,hasAgentExecution:!!M,hasThinkActSteps:(((S=M==null?void 0:M.thinkActSteps)==null?void 0:S.length)??0)>0,completed:ee,current:se,planCurrentStep:C.currentStepIndex,planCompleted:C.completed,isSubPlan:A}),M!=null&&M.thinkActSteps&&M.thinkActSteps.forEach((t,n)=>{t.subPlanExecutionRecord&&console.log(`[RightPanel] Found sub-plan in thinkActStep ${n}:`,t.subPlanExecutionRecord)}),setTimeout(()=>{L()},100),q()},E=(C,P,F,A)=>{console.log("[RightPanel] Sub plan step selected (delegating to unified handler):",{rootPlanId:C,subPlanId:P,stepIndex:F,subStepIndex:A}),V(P,A,C,P,A)},X=C=>{r.value=C??void 0},L=()=>{if(!r.value)return;const{scrollTop:C,scrollHeight:P,clientHeight:F}=r.value,A=P-C-F<50,y=P>F;I.value=A,x.value=y&&!A,A?D.value=!0:P-C-F>100&&(D.value=!1),console.log("[RightPanel] Scroll state check:",{scrollTop:C,scrollHeight:P,clientHeight:F,isAtBottom:A,hasScrollableContent:y,showButton:x.value,shouldAutoScroll:D.value})},O=()=>{r.value&&(r.value.scrollTo({top:r.value.scrollHeight,behavior:"smooth"}),ne(()=>{D.value=!0,L()}))},q=()=>{!D.value||!r.value||ne(()=>{r.value&&(r.value.scrollTop=r.value.scrollHeight,console.log("[RightPanel] Auto scroll to bottom"))})},Q=C=>{if(C===null||typeof C>"u"||C==="")return"N/A";try{const P=typeof C=="object"?C:JSON.parse(C);return JSON.stringify(P,null,2)}catch{return String(C)}},me=()=>{v.value=null,T.value=void 0,D.value=!0,r.value&&r.value.removeEventListener("scroll",L)},ue=()=>{const C=()=>{const P=r.value;return P?(X(P),P.addEventListener("scroll",L),D.value=!0,L(),console.log("[RightPanel] Scroll listener initialized successfully"),!0):(console.log("[RightPanel] Scroll container not found, retrying..."),!1)};ne(()=>{C()||setTimeout(()=>{C()},100)})};return Ce(()=>{console.log("[RightPanel] Component mounted"),ne(()=>{ue()})}),Ie(()=>{console.log("[RightPanel] Component unmounting, cleaning up..."),_.value=null,me()}),o({updateDisplayedPlanProgress:G,handleStepSelected:V,handleSubPlanStepSelected:E}),(C,P)=>{var F,A;return h(),g("div",vn,[e("div",fn,[e("div",bn,[e("button",kn,[k(l($),{icon:"carbon:events"}),Y(" "+i(l(a)("rightPanel.stepExecutionDetails")),1)])])]),e("div",_n,[e("div",$n,[v.value?(h(),g("div",Pn,[e("h3",null,i(v.value.title||v.value.description||l(a)("rightPanel.defaultStepTitle",{number:v.value.index+1})),1),v.value.agentExecution?(h(),g("div",Cn,[e("div",Sn,[e("span",yn,i(l(a)("rightPanel.executingAgent"))+":",1),e("span",En,i(v.value.agentExecution.agentName),1)]),e("div",wn,[e("span",Tn,i(l(a)("rightPanel.description"))+":",1),e("span",In,i(v.value.agentExecution.agentDescription||""),1)]),e("div",Dn,[e("span",xn,i(l(a)("rightPanel.callingModel"))+":",1),e("span",Rn,i(v.value.agentExecution.modelName),1)]),e("div",An,[e("span",Mn,i(l(a)("rightPanel.request"))+":",1),e("span",Nn,i(v.value.agentExecution.agentRequest||""),1)]),e("div",Un,[e("span",Ln,i(l(a)("rightPanel.executionResult"))+":",1),e("span",{class:te(["value",{success:v.value.agentExecution.status==="FINISHED"}])},i(v.value.agentExecution.status||l(a)("rightPanel.executing")),3)])])):B("",!0),e("div",Vn,[e("div",qn,[v.value.completed?(h(),ce(l($),{key:0,icon:"carbon:checkmark-filled",class:"status-icon success"})):v.value.current?(h(),ce(l($),{key:1,icon:"carbon:in-progress",class:"status-icon progress"})):(h(),ce(l($),{key:2,icon:"carbon:time",class:"status-icon pending"})),e("span",Fn,i(K.value),1)])])])):B("",!0),e("div",{ref_key:"scrollContainer",ref:r,class:"step-details-scroll-container",onScroll:L},[v.value?(h(),g("div",On,[(F=v.value.agentExecution)!=null&&F.thinkActSteps&&v.value.agentExecution.thinkActSteps.length>0?(h(),g("div",Bn,[e("h4",null,i(l(a)("rightPanel.thinkAndActionSteps")),1),e("div",Wn,[(h(!0),g(ge,null,fe(v.value.agentExecution.thinkActSteps,(y,M)=>(h(),g("div",{key:M,class:"think-act-step"},[e("div",jn,[e("span",Hn,"#"+i(M+1),1),e("span",{class:te(["step-status",y.status])},i(y.status||l(a)("rightPanel.executing")),3)]),e("div",Jn,[e("h5",null,[k(l($),{icon:"carbon:thinking"}),Y(" "+i(l(a)("rightPanel.thinking")),1)]),e("div",zn,[e("div",Gn,[e("span",Xn,i(l(a)("rightPanel.input"))+":",1),e("pre",null,i(Q(y.thinkInput)),1)]),e("div",Kn,[e("span",Qn,i(l(a)("rightPanel.output"))+":",1),e("pre",null,i(Q(y.thinkOutput)),1)])])]),y.actionNeeded?(h(),g("div",Yn,[e("h5",null,[k(l($),{icon:"carbon:play"}),Y(" "+i(l(a)("rightPanel.action")),1)]),e("div",Zn,[(h(!0),g(ge,null,fe(y.actToolInfoList,(ee,se)=>(h(),g("div",{key:se},[e("div",es,[e("span",ts,i(l(a)("rightPanel.tool"))+":",1),e("span",ns,i(ee.name||""),1)]),e("div",ss,[e("span",os,i(l(a)("rightPanel.toolParameters"))+":",1),e("pre",null,i(Q(ee.parameters)),1)]),e("div",as,[e("span",ls,i(l(a)("rightPanel.executionResult"))+":",1),e("pre",null,i(Q(ee.result)),1)])]))),128))]),y.subPlanExecutionRecord?(h(),g("div",is,[e("h5",null,[k(l($),{icon:"carbon:tree-view"}),Y(" "+i(l(a)("rightPanel.subPlan")),1)]),e("div",cs,[e("div",rs,[e("div",us,[P[0]||(P[0]=e("span",{class:"label"},"子计划ID:",-1)),e("span",ds,i(y.subPlanExecutionRecord.currentPlanId),1)]),y.subPlanExecutionRecord.title?(h(),g("div",ps,[P[1]||(P[1]=e("span",{class:"label"},"标题:",-1)),e("span",hs,i(y.subPlanExecutionRecord.title),1)])):B("",!0),e("div",gs,[y.subPlanExecutionRecord.completed?(h(),ce(l($),{key:0,icon:"carbon:checkmark-filled",class:"status-icon success"})):(h(),ce(l($),{key:1,icon:"carbon:in-progress",class:"status-icon progress"})),e("span",ms,i(y.subPlanExecutionRecord.completed?"已完成":"执行中"),1)])])])])):B("",!0)])):B("",!0)]))),128))]),v.value.agentExecution&&!((A=v.value.agentExecution.thinkActSteps)!=null&&A.length)?(h(),g("div",vs,[e("p",null,i(l(a)("rightPanel.noStepDetails")),1)])):v.value.agentExecution?B("",!0):(h(),g("div",fs,[k(l($),{icon:"carbon:information",class:"info-icon"}),e("h4",null,i(l(a)("rightPanel.stepInfo")),1),e("div",bs,[e("div",ks,[e("span",_s,i(l(a)("rightPanel.stepName"))+":",1),e("span",$s,i(v.value.title||v.value.description||`步骤 ${v.value.index+1}`),1)]),v.value.description?(h(),g("div",Ps,[P[2]||(P[2]=e("span",{class:"label"},"描述:",-1)),e("span",Cs,i(v.value.description),1)])):B("",!0),e("div",Ss,[P[3]||(P[3]=e("span",{class:"label"},"状态:",-1)),e("span",{class:te(["value",{"status-completed":v.value.completed,"status-current":v.value.current,"status-pending":!v.value.completed&&!v.value.current}])},i(v.value.completed?"已完成":v.value.current?"执行中":"待执行"),3)])]),e("p",ys,i(l(a)("rightPanel.noExecutionInfo")),1)])),v.value.current&&!v.value.completed?(h(),g("div",Es,[P[4]||(P[4]=e("div",{class:"execution-waves"},[e("div",{class:"wave wave-1"}),e("div",{class:"wave wave-2"}),e("div",{class:"wave wave-3"})],-1)),e("p",ws,[k(l($),{icon:"carbon:in-progress",class:"rotating-icon"}),Y(" "+i(l(a)("rightPanel.stepExecuting")),1)])])):B("",!0)])):(h(),g("div",Ts,[k(l($),{icon:"carbon:events",class:"empty-icon"}),e("h3",null,i(l(a)("rightPanel.noStepSelected")),1),e("p",null,i(l(a)("rightPanel.selectStepHint")),1)]))])):B("",!0),k(De,{name:"scroll-button"},{default:xe(()=>[x.value?(h(),g("button",{key:0,onClick:O,class:"scroll-to-bottom-btn",title:l(a)("rightPanel.scrollToBottom")},[k(l($),{icon:"carbon:chevron-down"})],8,Is)):B("",!0)]),_:1})],544)])])])}}}),xs=Se(Ds,[["__scopeId","data-v-e90596ce"]]);function Rs(){const w=oe,o=ke(()=>w.getActivePlanId()),a=ke(()=>w.getState()),r=ke(()=>a.value.isPolling),T=ke(()=>!!o.value),v=(D,K)=>{w.initiatePlanExecutionSequence(D,K)},_=()=>{w.stopPolling()},x=()=>{w.startPolling()},I=()=>{w.cleanup()};return Ie(()=>{I()}),{activePlanId:o,state:a,isPolling:r,hasActivePlan:T,startExecution:v,stopPolling:_,startPolling:x,cleanup:I}}const As={class:"chat-container"},Ms={class:"message-content"},Ns={key:0,class:"user-message"},Us={key:1,class:"assistant-message"},Ls={key:0,class:"thinking-section"},Vs={class:"thinking-header"},qs={class:"thinking-avatar"},Fs={class:"thinking-label"},Os={class:"thinking-content"},Bs={key:0,class:"thinking"},Ws={key:1,class:"progress"},js={class:"progress-bar"},Hs={class:"progress-text"},Js={key:2,class:"steps-container"},zs={class:"steps-title"},Gs=["onClick"],Xs={class:"section-header"},Ks={class:"step-icon"},Qs={class:"step-title"},Ys={key:0,class:"step-status current"},Zs={key:1,class:"step-status completed"},eo={key:2,class:"step-status pending"},to={key:0,class:"action-info"},no={class:"action-description"},so={class:"action-icon"},oo={key:0,class:"tool-params"},ao={class:"param-label"},lo={class:"param-content"},io={key:1,class:"think-details"},co={class:"think-header"},ro={class:"think-label"},uo={class:"think-output"},po={class:"think-content"},ho={key:1,class:"sub-plan-steps"},go={class:"sub-plan-header"},mo={class:"sub-plan-step-list"},vo=["onClick"],fo={class:"sub-step-indicator"},bo={class:"sub-step-icon"},ko={class:"sub-step-number"},_o={class:"sub-step-content"},$o={class:"sub-step-title"},Po={key:2,class:"user-input-form-container"},Co={class:"user-input-message"},So={key:0,class:"form-description"},yo=["onSubmit"],Eo=["for"],wo=["id","name","onUpdate:modelValue"],To={key:1,class:"form-group"},Io={for:"form-input-genericInput"},Do=["onUpdate:modelValue"],xo={type:"submit",class:"submit-user-input-btn"},Ro={key:3,class:"default-processing"},Ao={class:"processing-indicator"},Mo={class:"response-section"},No={class:"response-header"},Uo={class:"response-avatar"},Lo={class:"response-name"},Vo={class:"response-content"},qo={key:0,class:"final-response"},Fo=["innerHTML"],Oo={key:1,class:"response-placeholder"},Bo={class:"typing-indicator"},Wo={class:"typing-text"},jo={key:0,class:"message assistant"},Ho={class:"message-content"},Jo={class:"assistant-message"},zo={class:"thinking-section"},Go={class:"thinking-header"},Xo={class:"thinking-avatar"},Ko={class:"thinking-label"},Qo={class:"thinking-content"},Yo={class:"default-processing"},Zo={class:"processing-indicator"},ea={class:"response-section"},ta={class:"response-header"},na={class:"response-avatar"},sa={class:"response-name"},oa={class:"response-content"},aa={class:"response-placeholder"},la={class:"typing-indicator"},ia={class:"typing-text"},ca=["title"],ra=Pe({__name:"index",props:{mode:{default:"plan"},initialPrompt:{default:""}},emits:["step-selected","sub-plan-step-selected"],setup(w,{expose:o,emit:a}){const r=w,T=a,{t:v}=Te(),_=Rs(),x=R(),I=R(!1),D=R([]),K=R(),G=R(!1),V=ot({}),s=(t,n,c)=>{const p={id:Date.now().toString(),type:t,content:n,timestamp:new Date,...c};return t==="assistant"&&!p.thinking&&!p.content&&(p.thinking=v("chat.thinking")),D.value.push(p),p},d=t=>{const n=D.value[D.value.length-1];n.type==="assistant"&&Object.assign(n,t)},E=async t=>{try{I.value=!0;const n=s("assistant","",{thinking:"正在理解您的请求并准备回复..."}),c=await Le.sendMessage(t);if(c.planId)console.log("[ChatComponent] Received planId from direct execution:",c.planId),n.planExecution||(n.planExecution={}),n.planExecution.currentPlanId=c.planId,oe.handlePlanExecutionRequested(c.planId,t),delete n.thinking,console.log("[ChatComponent] Started polling for plan execution updates");else{delete n.thinking;const p=X(c,t);n.content=p}}catch(n){console.error("Direct mode error:",n),d({content:L(n)})}finally{I.value=!1}},X=(t,n)=>t.result??t.message??t.content??"",L=t=>{const n=(t==null?void 0:t.message)??(t==null?void 0:t.toString())??"未知错误";return n.includes("网络")||n.includes("network")||n.includes("timeout")?"抱歉，似乎网络连接有些问题。请检查您的网络连接后再试一次，或者稍等几分钟再重新提问。":n.includes("认证")||n.includes("权限")||n.includes("auth")?"抱歉，访问权限出现了问题。这可能是系统配置的问题，请联系管理员或稍后再试。":n.includes("格式")||n.includes("参数")||n.includes("invalid")?"抱歉，您的请求格式可能有些问题。能否请您重新表述一下您的需求？我会尽力理解并帮助您。":`抱歉，处理您的请求时遇到了一些问题（${n}）。请稍后再试，或者换个方式表达您的需求，我会尽力帮助您的。`},O=(t=!1)=>{ne(()=>{if(x.value){const n=x.value;(t||n.scrollHeight-n.scrollTop-n.clientHeight<150)&&n.scrollTo({top:n.scrollHeight,behavior:t?"auto":"smooth"})}})},q=()=>{O(!0),G.value=!1},Q=()=>{if(x.value){const t=x.value,n=t.scrollHeight-t.scrollTop-t.clientHeight<150;G.value=!n&&D.value.length>0}},me=()=>{x.value&&x.value.addEventListener("scroll",Q)},ue=()=>{x.value&&x.value.removeEventListener("scroll",Q)},C=t=>{s("user",t),r.mode==="plan"?console.log("[ChatComponent] Plan mode message sent, parent should handle:",t):E(t)},P=(t,n)=>{var W;const c=((W=t.planExecution)==null?void 0:W.agentExecutionSequence)??[];return n<0||n>=c.length?"IDLE":c[n].status??"IDLE"},F=(t,n)=>{var c,p;if(!((c=t.planExecution)!=null&&c.currentPlanId)){console.warn("[ChatComponent] Cannot handle step click: missing currentPlanId");return}console.log("[ChatComponent] Step clicked:",{planId:t.planExecution.currentPlanId,stepIndex:n,stepTitle:(p=t.planExecution.steps)==null?void 0:p[n]}),T("step-selected",t.planExecution.currentPlanId,n)},A=(t,n)=>{var c;try{const p=(c=t.planExecution)==null?void 0:c.agentExecutionSequence;if(!(p!=null&&p.length))return console.log("[ChatComponent] No agentExecutionSequence found"),[];const W=p[n];if(!W)return console.log(`[ChatComponent] No agentExecution found for step ${n}`),[];if(!W.thinkActSteps)return console.log(`[ChatComponent] No thinkActSteps found for step ${n}`),[];for(const U of W.thinkActSteps)if(U.subPlanExecutionRecord)return console.log(`[ChatComponent] Found sub-plan for step ${n}:`,U.subPlanExecutionRecord),(U.subPlanExecutionRecord.steps??[]).map(j=>typeof j=="string"?j:typeof j=="object"&&j!==null&&(j.title||j.description)||"子步骤");return[]}catch(p){return console.warn("[ChatComponent] Error getting sub-plan steps:",p),[]}},y=(t,n,c)=>{var p;try{const W=(p=t.planExecution)==null?void 0:p.agentExecutionSequence;if(!(W!=null&&W.length))return"pending";const U=W[n];if(!U||!U.thinkActSteps)return"pending";let Z=null;for(const H of U.thinkActSteps)if(H.subPlanExecutionRecord){Z=H.subPlanExecutionRecord;break}if(!Z)return"pending";const j=Z.currentStepIndex;return Z.completed?"completed":j==null?c===0?"current":"pending":c<j?"completed":c===j?"current":"pending"}catch(W){return console.warn("[ChatComponent] Error getting sub-plan step status:",W),"pending"}},M=(t,n,c)=>{var p,W;try{const U=(p=t.planExecution)==null?void 0:p.agentExecutionSequence;if(!(U!=null&&U.length)){console.warn("[ChatComponent] No agentExecutionSequence data for sub-plan step click");return}const Z=U[n];if(!Z){console.warn("[ChatComponent] No agentExecution found for step",n);return}if(!Z.thinkActSteps){console.warn("[ChatComponent] No thinkActSteps found for step",n);return}let j=null;for(const H of Z.thinkActSteps)if(H.subPlanExecutionRecord){j=H.subPlanExecutionRecord;break}if(!(j!=null&&j.currentPlanId)){console.warn("[ChatComponent] No sub-plan data for step click");return}T("sub-plan-step-selected",((W=t.planExecution)==null?void 0:W.currentPlanId)??"",j.currentPlanId,n,c)}catch(U){console.error("[ChatComponent] Error handling sub-plan step click:",U)}},ee=(t,n)=>{var p,W,U,Z;if(!((p=t.planExecution)!=null&&p.steps))return;console.log("[ChatComponent] Starting to update step actions, steps count:",t.planExecution.steps.length,"execution sequence:",((W=n.agentExecutionSequence)==null?void 0:W.length)??0);const c=new Array(t.planExecution.steps.length).fill(null);if((U=n.agentExecutionSequence)!=null&&U.length){const j=Math.min(n.agentExecutionSequence.length,t.planExecution.steps.length);for(let H=0;H<j;H++){const N=n.agentExecutionSequence[H];if((Z=N.thinkActSteps)!=null&&Z.length){const J=N.thinkActSteps[N.thinkActSteps.length-1];J.actionDescription&&J.toolParameters?(c[H]={actionDescription:J.actionDescription,toolParameters:typeof J.toolParameters=="string"?J.toolParameters:JSON.stringify(J.toolParameters,null,2),thinkInput:J.thinkInput??"",thinkOutput:J.thinkOutput??"",status:n.currentStepIndex!==void 0&&H<n.currentStepIndex?"completed":n.currentStepIndex!==void 0&&H===n.currentStepIndex?"current":"pending"},console.log(`[ChatComponent] Step ${H} action set: ${c[H].actionDescription}`)):(c[H]={actionDescription:"思考中",toolParameters:"等待决策",thinkInput:J.thinkInput??"",thinkOutput:J.thinkOutput??"",status:n.currentStepIndex!==void 0&&H===n.currentStepIndex?"current":"pending"},console.log(`[ChatComponent] Step ${H} is thinking`))}else c[H]={actionDescription:n.currentStepIndex!==void 0&&H<n.currentStepIndex?"已完成":"等待中",toolParameters:"无工具参数",thinkInput:"",thinkOutput:"",status:n.currentStepIndex!==void 0&&H<n.currentStepIndex?"completed":"pending"},console.log(`[ChatComponent] 步骤 ${H} 无执行细节, 状态设为: ${c[H].status}`)}}else console.log("[ChatComponent] 没有执行序列数据");t.stepActions=[...c],console.log("[ChatComponent] 步骤动作更新完成:",JSON.stringify(c.map(j=>j==null?void 0:j.actionDescription))),ne(()=>{console.log("[ChatComponent] UI update completed via reactivity")})},se=t=>{console.log("[ChatComponent] Starting dialog round with planId:",t),t&&(D.value.findIndex(c=>{var p;return((p=c.planExecution)==null?void 0:p.currentPlanId)===t&&c.type==="assistant"})===-1?(s("assistant","",{planExecution:{currentPlanId:t},thinking:"正在准备执行计划..."}),console.log("[ChatComponent] Created new assistant message for planId:",t)):console.log("[ChatComponent] Found existing assistant message for planId:",t))},ve=t=>{var U,Z,j,H;console.log("[ChatComponent] Processing plan update with rootPlanId:",t);const n=oe.getCachedPlanRecord(t);if(!n){console.warn("[ChatComponent] No cached plan data found for rootPlanId:",t);return}if(console.log("[ChatComponent] Retrieved plan details from cache:",n),console.log("[ChatComponent] Plan steps:",n.steps),console.log("[ChatComponent] Plan completed:",n.completed),!n.currentPlanId){console.warn("[ChatComponent] Plan update missing currentPlanId");return}const c=D.value.findIndex(N=>{var J;return((J=N.planExecution)==null?void 0:J.currentPlanId)===n.currentPlanId&&N.type==="assistant"});let p;if(c!==-1)p=D.value[c],console.log("[ChatComponent] Found existing assistant message for currentPlanId:",n.currentPlanId);else{console.warn("[ChatComponent] No existing assistant message found for currentPlanId:",n.currentPlanId),console.log("[ChatComponent] Current messages:",D.value.map(J=>{var ae;return{type:J.type,planId:(ae=J.planExecution)==null?void 0:ae.currentPlanId,content:J.content.substring(0,50)}}));let N=-1;for(let J=D.value.length-1;J>=0;J--)if(D.value[J].type==="assistant"){N=J;break}if(N!==-1)p=D.value[N],p.planExecution||(p.planExecution={}),p.planExecution.currentPlanId=n.currentPlanId,console.log("[ChatComponent] Using last assistant message and updating planExecution.currentPlanId to:",n.currentPlanId);else{console.error("[ChatComponent] No assistant message found at all, this should not happen");return}}if(p.planExecution||(p.planExecution={}),p.planExecution=JSON.parse(JSON.stringify(n)),!n.steps||n.steps.length===0){if(console.log("[ChatComponent] Handling simple response without steps"),n.completed){delete p.thinking;const N=n.summary??n.result??n.message??"处理完成";p.content=de(N),console.log("[ChatComponent] Set simple response content:",p.content)}else n.title&&(p.thinking=`正在执行: ${n.title}`);return}delete p.thinking;const W=n.steps.map(N=>typeof N=="string"?N:typeof N=="object"&&N!==null&&(N.title||N.description)||"步骤");if(p.planExecution&&(p.planExecution.steps=W),n.agentExecutionSequence&&n.agentExecutionSequence.length>0){console.log("[ChatComponent] 发现执行序列数据，数量:",n.agentExecutionSequence.length),ee(p,n);const N=n.currentStepIndex??0;if(N>=0&&N<n.agentExecutionSequence.length){const ae=n.agentExecutionSequence[N].thinkActSteps;if(ae&&ae.length>0){const ye=ae[ae.length-1];if(ye.thinkOutput){const Re=ye.thinkOutput.length>150?ye.thinkOutput.substring(0,150)+"...":ye.thinkOutput;p.thinking=`正在思考: ${Re}`}}}}else if(p.planExecution){const N=p.planExecution.currentStepIndex??0,J=(U=p.planExecution.steps)==null?void 0:U[N],ae=typeof J=="string"?J:"";p.thinking=`正在执行: ${ae}`}if(n.userInputWaitState&&p.planExecution?(console.log("[ChatComponent] 需要用户输入:",n.userInputWaitState),p.planExecution.userInputWaitState||(p.planExecution.userInputWaitState={}),p.planExecution.userInputWaitState={message:n.userInputWaitState.message??"",formDescription:n.userInputWaitState.formDescription??"",formInputs:((Z=n.userInputWaitState.formInputs)==null?void 0:Z.map(N=>({label:N.label,value:N.value||""})))??[]},V[j=p.id]??(V[j]={}),p.thinking="等待用户输入..."):(H=p.planExecution)!=null&&H.userInputWaitState&&delete p.planExecution.userInputWaitState,n.completed??n.status==="completed"){console.log("[ChatComponent] Plan is completed, updating final response"),delete p.thinking;let N="";n.summary?N=n.summary:n.result?N=n.result:N="任务已完成",p.content=b(N),console.log("[ChatComponent] Updated completed message:",p.content)}ne(()=>{console.log("[ChatComponent] Plan update UI refresh completed via reactivity")})},de=t=>t?t.includes("我")||t.includes("您")||t.includes("您好")||t.includes("可以")?t:t.length<10?`${t}！还有什么需要我帮助的吗？`:t.length<50?`好的，${t}。如果您还有其他问题，请随时告诉我。`:`${t}

希望这个回答对您有帮助！还有什么我可以为您做的吗？`:"我明白了，还有什么我可以帮您的吗？",b=t=>t?`${t}`:"任务已完成！还有什么我可以帮您的吗？",m=t=>{console.log("[ChatComponent] Plan completed with rootPlanId:",t);const n=oe.getCachedPlanRecord(t);if(!n){console.warn("[ChatComponent] No cached plan data found for rootPlanId:",t);return}if(console.log("[ChatComponent] Plan details:",n),n.rootPlanId){const c=D.value.findIndex(p=>{var W;return((W=p.planExecution)==null?void 0:W.currentPlanId)===n.rootPlanId});if(c!==-1){const p=D.value[c];delete p.thinking;let U=n.summary??n.result??"任务已完成";!U.includes("我")&&!U.includes("您")&&(U.includes("成功")||U.includes("完成")?U=`很好！${U}。如果您还有其他需要帮助的地方，请随时告诉我。`:U=`我已经完成了您的请求：${U}`),p.content=U,console.log("[ChatComponent] Updated completed message:",p.content)}else console.warn("[ChatComponent] No message found for completed rootPlanId:",n.rootPlanId)}},u=t=>{if(!t)return"";let n=t.replace(/\n\n/g,"<br><br>").replace(/\n/g,"<br>");return n=n.replace(/(<br><br>)/g,"</p><p>"),n.includes("</p><p>")&&(n=`<p>${n}</p>`),n},S=async t=>{var n;if(!((n=t.planExecution)!=null&&n.currentPlanId)||!t.planExecution.userInputWaitState){console.error("[ChatComponent] 缺少planExecution.currentPlanId或userInputWaitState");return}try{const c={},p=t.planExecution.userInputWaitState.formInputs;p&&p.length>0?Object.entries(V[t.id]).forEach(([U,Z])=>{var N;const j=parseInt(U,10),H=((N=p[j])==null?void 0:N.label)||`input_${U}`;c[H]=Z}):c.genericInput=t.genericInput??"",console.log("[ChatComponent] 提交用户输入:",c);const W=await Ve.submitFormInput(t.planExecution.currentPlanId,c);delete t.planExecution.userInputWaitState,delete t.genericInput,delete V[t.id],_.startPolling(),console.log("[ChatComponent] 用户输入提交成功:",W)}catch(c){console.error("[ChatComponent] 用户输入提交失败:",c),alert(`提交失败: ${(c==null?void 0:c.message)||"未知错误"}`)}};return _e(()=>r.initialPrompt,(t,n)=>{console.log("[ChatComponent] initialPrompt changed from:",n,"to:",t),t&&typeof t=="string"&&t.trim()&&t!==n&&(console.log("[ChatComponent] Processing changed initial prompt:",t),ne(()=>{C(t)}))},{immediate:!1}),Ce(()=>{console.log("[ChatComponent] Mounted, setting up event listeners"),oe.setEventCallbacks({onPlanUpdate:ve,onPlanCompleted:m,onDialogRoundStart:se,onChatInputUpdateState:t=>{console.log("[ChatComponent] Chat input state update for rootPlanId:",t)},onChatInputClear:()=>{console.log("[ChatComponent] Chat input clear requested")}}),ne(()=>{me()}),r.initialPrompt&&typeof r.initialPrompt=="string"&&r.initialPrompt.trim()&&(console.log("[ChatComponent] Processing initial prompt:",r.initialPrompt),ne(()=>{C(r.initialPrompt)}))}),Ie(()=>{console.log("[ChatComponent] Unmounting, cleaning up resources"),ue(),K.value&&clearInterval(K.value),_.cleanup(),Object.keys(V).forEach(t=>delete V[t])}),o({handleSendMessage:C,handlePlanUpdate:ve,handlePlanCompleted:m,handleDialogRoundStart:se,addMessage:s}),(t,n)=>(h(),g("div",As,[e("div",{class:"messages",ref_key:"messagesRef",ref:x},[(h(!0),g(ge,null,fe(D.value,c=>{var p,W,U,Z,j,H,N,J,ae;return h(),g("div",{key:c.id,class:te(["message",{user:c.type==="user",assistant:c.type==="assistant"}])},[e("div",Ms,[c.type==="user"?(h(),g("div",Ns,i(c.content),1)):(h(),g("div",Us,[c.thinking||((p=c.planExecution)==null?void 0:p.progress)!==void 0||(((U=(W=c.planExecution)==null?void 0:W.steps)==null?void 0:U.length)??0)>0?(h(),g("div",Ls,[e("div",Vs,[e("div",qs,[k(l($),{icon:"carbon:thinking",class:"thinking-icon"})]),e("div",Fs,i(t.$t("chat.thinkingLabel")),1)]),e("div",Os,[c.thinking?(h(),g("div",Bs,[k(l($),{icon:"carbon:thinking",class:"thinking-icon"}),e("span",null,i(c.thinking),1)])):B("",!0),((Z=c.planExecution)==null?void 0:Z.progress)!==void 0?(h(),g("div",Ws,[e("div",js,[e("div",{class:"progress-fill",style:Ne({width:c.planExecution.progress+"%"})},null,4)]),e("span",Hs,i(c.planExecution.progressText??t.$t("chat.processing")+"..."),1)])):B("",!0),(((H=(j=c.planExecution)==null?void 0:j.steps)==null?void 0:H.length)??0)>0?(h(),g("div",Js,[e("h4",zs,i(t.$t("chat.stepExecutionDetails")),1),(h(!0),g(ge,null,fe((N=c.planExecution)==null?void 0:N.steps,(ye,z)=>{var Re,qe,Fe,Oe,Be,We,je,He,Je,ze,Ge,Xe,Ke,Qe,Ye,Ze,et,tt,nt;return h(),g("div",{key:z,class:te(["ai-section",{running:P(c,z)==="RUNNING",completed:P(c,z)==="FINISHED",pending:P(c,z)==="IDLE"}]),onClick:ie(pe=>F(c,z),["stop"])},[e("div",Xs,[e("span",Ks,i(P(c,z)==="FINISHED"?"✓":P(c,z)==="RUNNING"?"▶":"○"),1),e("span",Qs,i(ye||`${t.$t("chat.step")} ${z+1}`),1),P(c,z)==="RUNNING"?(h(),g("span",Ys,i(t.$t("chat.status.executing")),1)):P(c,z)==="FINISHED"?(h(),g("span",Zs,i(t.$t("chat.status.completed")),1)):(h(),g("span",eo,i(t.$t("chat.status.pending")),1))]),c.stepActions&&c.stepActions[z]?(h(),g("div",to,[e("div",no,[e("span",so,i(((Re=c.stepActions[z])==null?void 0:Re.status)==="current"?"🔄":((qe=c.stepActions[z])==null?void 0:qe.status)==="completed"?"✓":"⏳"),1),e("strong",null,i((Fe=c.stepActions[z])==null?void 0:Fe.actionDescription),1)]),(Oe=c.stepActions[z])!=null&&Oe.toolParameters?(h(),g("div",oo,[n[0]||(n[0]=e("span",{class:"tool-icon"},"⚙️",-1)),e("span",ao,i(t.$t("common.parameters"))+":",1),e("pre",lo,i((Be=c.stepActions[z])==null?void 0:Be.toolParameters),1)])):B("",!0),(We=c.stepActions[z])!=null&&We.thinkOutput?(h(),g("div",io,[e("div",co,[n[1]||(n[1]=e("span",{class:"think-icon"},"💭",-1)),e("span",ro,i(t.$t("chat.thinkingOutput"))+":",1)]),e("div",uo,[e("pre",po,i((je=c.stepActions[z])==null?void 0:je.thinkOutput),1)])])):B("",!0)])):B("",!0),((He=A(c,z))==null?void 0:He.length)>0?(h(),g("div",ho,[e("div",go,[k(l($),{icon:"carbon:tree-view",class:"sub-plan-icon"}),n[2]||(n[2]=e("span",{class:"sub-plan-title"},"子执行计划",-1))]),e("div",mo,[(h(!0),g(ge,null,fe(A(c,z),(pe,le)=>(h(),g("div",{key:`sub-${z}-${le}`,class:te(["sub-plan-step-item",{completed:y(c,z,le)==="completed",current:y(c,z,le)==="current",pending:y(c,z,le)==="pending"}]),onClick:ie(st=>M(c,z,le),["stop"])},[e("div",fo,[e("span",bo,i(y(c,z,le)==="completed"?"✓":y(c,z,le)==="current"?"▶":"○"),1),e("span",ko,i(le+1),1)]),e("div",_o,[e("span",$o,i(pe),1),n[3]||(n[3]=e("span",{class:"sub-step-badge"},"子步骤",-1))])],10,vo))),128))])])):B("",!0),(Je=c.planExecution)!=null&&Je.userInputWaitState&&P(c,z)==="RUNNING"?(h(),g("div",Po,[e("p",Co,i(((Ge=(ze=c.planExecution)==null?void 0:ze.userInputWaitState)==null?void 0:Ge.message)??t.$t("chat.userInput.message")),1),(Ke=(Xe=c.planExecution)==null?void 0:Xe.userInputWaitState)!=null&&Ke.formDescription?(h(),g("p",So,i((Ye=(Qe=c.planExecution)==null?void 0:Qe.userInputWaitState)==null?void 0:Ye.formDescription),1)):B("",!0),e("form",{onSubmit:ie(pe=>S(c),["prevent"]),class:"user-input-form"},[(et=(Ze=c.planExecution)==null?void 0:Ze.userInputWaitState)!=null&&et.formInputs&&c.planExecution.userInputWaitState.formInputs.length>0?(h(!0),g(ge,{key:0},fe((nt=(tt=c.planExecution)==null?void 0:tt.userInputWaitState)==null?void 0:nt.formInputs,(pe,le)=>(h(),g("div",{key:le,class:"form-group"},[e("label",{for:`form-input-${pe.label.replace(/\W+/g,"_")}`},i(pe.label)+": ",9,Eo),re(e("input",{type:"text",id:`form-input-${pe.label.replace(/\W+/g,"_")}`,name:pe.label,"onUpdate:modelValue":st=>V[c.id][le]=st,class:"form-input"},null,8,wo),[[be,V[c.id][le]]])]))),128)):(h(),g("div",To,[e("label",Io,i(t.$t("common.input"))+":",1),re(e("input",{type:"text",id:"form-input-genericInput",name:"genericInput","onUpdate:modelValue":pe=>c.genericInput=pe,class:"form-input"},null,8,Do),[[be,c.genericInput]])])),e("button",xo,i(t.$t("chat.userInput.submit")),1)],40,yo)])):B("",!0)],10,Gs)}),128))])):!c.content&&(c.thinking||((J=c.planExecution)==null?void 0:J.progress)!==void 0&&(((ae=c.planExecution)==null?void 0:ae.progress)??0)<100)?(h(),g("div",Ro,[e("div",Ao,[n[4]||(n[4]=e("div",{class:"thinking-dots"},[e("span"),e("span"),e("span")],-1)),e("span",null,i(c.thinking??t.$t("chat.thinkingProcessing")),1)])])):B("",!0)])])):B("",!0),e("div",Mo,[e("div",No,[e("div",Uo,[k(l($),{icon:"carbon:bot",class:"bot-icon"})]),e("div",Lo,i(t.$t("chat.botName")),1)]),e("div",Vo,[c.content?(h(),g("div",qo,[e("div",{class:"response-text",innerHTML:u(c.content)},null,8,Fo)])):(h(),g("div",Oo,[e("div",Bo,[n[5]||(n[5]=e("div",{class:"typing-dots"},[e("span"),e("span"),e("span")],-1)),e("span",Wo,i(t.$t("chat.thinkingResponse")),1)])]))])])]))])],2)}),128)),I.value?(h(),g("div",jo,[e("div",Ho,[e("div",Jo,[e("div",zo,[e("div",Go,[e("div",Xo,[k(l($),{icon:"carbon:thinking",class:"thinking-icon"})]),e("div",Ko,i(t.$t("chat.thinkingLabel")),1)]),e("div",Qo,[e("div",Yo,[e("div",Zo,[n[6]||(n[6]=e("div",{class:"thinking-dots"},[e("span"),e("span"),e("span")],-1)),e("span",null,i(t.$t("chat.thinking")),1)])])])]),e("div",ea,[e("div",ta,[e("div",na,[k(l($),{icon:"carbon:bot",class:"bot-icon"})]),e("div",sa,i(t.$t("chat.botName")),1)]),e("div",oa,[e("div",aa,[e("div",la,[n[7]||(n[7]=e("div",{class:"typing-dots"},[e("span"),e("span"),e("span")],-1)),e("span",ia,i(t.$t("chat.thinkingResponse")),1)])])])])])])])):B("",!0)],512),G.value?(h(),g("div",{key:0,class:"scroll-to-bottom-btn",onClick:q,title:t.$t("chat.scrollToBottom")},[k(l($),{icon:"carbon:chevron-down"})],8,ca)):B("",!0)]))}}),ua=Se(ra,[["__scopeId","data-v-d1fb4f30"]]),da={class:"input-area"},pa={class:"input-container"},ha={class:"attach-btn",title:"附加文件"},ga=["placeholder","disabled"],ma=["title"],va=["disabled","title"],fa=Pe({__name:"index",props:{placeholder:{default:""},disabled:{type:Boolean,default:!1},initialValue:{default:""}},emits:["send","clear","update-state","plan-mode-clicked"],setup(w,{expose:o,emit:a}){const{t:r}=Te(),T=w,v=a,_=R(),x=R(""),I=ke(()=>T.placeholder||r("input.placeholder")),D=R(I.value),K=ke(()=>!!T.disabled),G=()=>{ne(()=>{_.value&&(_.value.style.height="auto",_.value.style.height=Math.min(_.value.scrollHeight,120)+"px")})},V=q=>{q.key==="Enter"&&!q.shiftKey&&(q.preventDefault(),s())},s=()=>{if(!x.value.trim()||K.value)return;const q=x.value.trim();v("send",q),E()},d=()=>{v("plan-mode-clicked")},E=()=>{x.value="",G(),v("clear")},X=(q,Q)=>{Q&&(D.value=q?Q:r("input.waiting")),v("update-state",q,Q)},L=q=>{x.value=q,G()},O=()=>x.value.trim();return _e(()=>T.initialValue,q=>{q&&q.trim()&&(x.value=q,G())},{immediate:!0}),o({clearInput:E,updateState:X,setInputValue:L,getQuery:O,focus:()=>{var q;return(q=_.value)==null?void 0:q.focus()}}),Ce(()=>{}),Ie(()=>{}),(q,Q)=>(h(),g("div",da,[e("div",pa,[e("button",ha,[k(l($),{icon:"carbon:attachment"})]),re(e("textarea",{"onUpdate:modelValue":Q[0]||(Q[0]=me=>x.value=me),ref_key:"inputRef",ref:_,class:"chat-input",placeholder:D.value,disabled:K.value,onKeydown:V,onInput:G},null,40,ga),[[be,x.value]]),e("button",{class:"plan-mode-btn",title:q.$t("input.planMode"),onClick:d},[k(l($),{icon:"carbon:document"}),Y(" "+i(q.$t("input.planMode")),1)],8,ma),e("button",{class:"send-button",disabled:!x.value.trim()||K.value,onClick:s,title:q.$t("input.send")},[k(l($),{icon:"carbon:send-alt"}),Y(" "+i(q.$t("input.send")),1)],8,va)])]))}}),ba=Se(fa,[["__scopeId","data-v-639c8b2a"]]);class Ee{static async getAllCronTasks(){try{const o=await fetch(this.BASE_URL);return await(await this.handleResponse(o)).json()}catch(o){throw console.error("Failed to get cron tasks:",o),o}}static async getCronTaskById(o){try{const a=await fetch(`${this.BASE_URL}/${o}`);return await(await this.handleResponse(a)).json()}catch(a){throw console.error("Failed to get cron task by id:",a),a}}static async createCronTask(o){try{const a=await fetch(this.BASE_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});return await(await this.handleResponse(a)).json()}catch(a){throw console.error("Failed to create cron task:",a),a}}static async updateCronTask(o,a){try{const r=await fetch(`${this.BASE_URL}/${o}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});return await(await this.handleResponse(r)).json()}catch(r){throw console.error("Failed to update cron task:",r),r}}static async updateTaskStatus(o,a){try{const r=await fetch(`${this.BASE_URL}/${o}/status?status=${a}`,{method:"PUT"});await this.handleResponse(r)}catch(r){throw console.error("Failed to update task status:",r),r}}static async deleteCronTask(o){try{const a=await fetch(`${this.BASE_URL}/${o}`,{method:"DELETE"});await this.handleResponse(a)}catch(a){throw console.error("Failed to delete cron task:",a),a}}static async handleResponse(o){if(!o.ok)try{const a=await o.json();throw new Error(a.message||`API request failed: ${o.status}`)}catch{throw new Error(`API request failed: ${o.status} ${o.statusText}`)}return o}}he(Ee,"BASE_URL","/api/cron-tasks");const we={validateCronExpression(w){const o=w.trim().split(/\s+/);return o.length>=5&&o.length<=6},formatTime(w){return new Date(w).toLocaleString()},async saveTask(w){try{let o;return w.id?o=await Ee.updateCronTask(Number(w.id),w):o=await Ee.createCronTask(w),o}catch(o){throw console.error("Failed to save cron task:",o),o}},async deleteTask(w){try{await Ee.deleteCronTask(String(w))}catch(o){throw console.error("Failed to delete cron task:",o),o}},async toggleTaskStatus(w){if(!w.id)throw new Error("Task ID is required");const o=w.status===0?1:0;return await Ee.updateCronTask(Number(w.id),{...w,status:o})},prepareTaskExecution(w){return w.planTemplateId?{useTemplate:!0,planData:{title:w.cronName||"定时任务执行",planData:{id:w.planTemplateId,planTemplateId:w.planTemplateId,planId:w.planTemplateId},params:w.executionParams||void 0}}:{useTemplate:!1,taskContent:w.planDesc||w.cronName||""}}},ka={class:"modal-header"},_a={class:"header-actions"},$a={class:"status-switch"},Pa={class:"status-label"},Ca={class:"toggle-switch"},Sa=["checked"],ya={class:"modal-content"},Ea={class:"form-group"},wa={class:"form-label"},Ta=["placeholder"],Ia={class:"form-group"},Da={class:"form-label"},xa=["placeholder"],Ra={class:"form-help"},Aa={class:"form-group"},Ma={class:"form-label"},Na=["placeholder"],Ua={class:"form-group"},La={class:"form-label"},Va={class:"template-toggle"},qa={key:0,class:"template-selector"},Fa={value:""},Oa=["value"],Ba={class:"form-help"},Wa={key:0,class:"form-group"},ja={class:"time-info"},Ha={class:"time-label"},Ja={class:"time-value"},za={key:1,class:"form-group"},Ga={class:"time-info"},Xa={class:"time-label"},Ka={class:"time-value"},Qa={class:"modal-footer"},Ya=["disabled"],Za=Pe({__name:"TaskDetailModal",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue","save"],setup(w,{emit:o}){const a=w,r=o,T=R(!1),v=R([]),_=R({cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""});Ce(async()=>{try{const s=await Me.getAllPlanTemplates();s&&s.templates&&(v.value=s.templates.map(d=>({id:d.id,name:d.title||"Unnamed Template"})))}catch(s){console.error("Failed to get template list:",s)}});const I=s=>{s.target===s.currentTarget&&r("update:modelValue",!1)},D=()=>{_.value.linkTemplate=!1,_.value.templateId="",_.value.planTemplateId=""},K=()=>_.value.cronName.trim()?_.value.cronTime.trim()?we.validateCronExpression(_.value.cronTime)?_.value.planDesc.trim()?_.value.linkTemplate&&!_.value.templateId?(alert("Please select a plan template"),!1):!0:(alert("Task description cannot be empty"),!1):(alert("Invalid Cron expression format, should be 5-6 parts separated by spaces"),!1):(alert("Cron expression cannot be empty"),!1):(alert("Task name cannot be empty"),!1),G=s=>we.formatTime(s),V=async()=>{var s;if(K()){T.value=!0;try{const d={..._.value,...((s=a.task)==null?void 0:s.id)!==void 0&&{id:a.task.id},cronName:_.value.cronName.trim(),cronTime:_.value.cronTime.trim(),planDesc:_.value.planDesc.trim(),status:_.value.status,planTemplateId:_.value.linkTemplate&&_.value.templateId||""};r("save",d)}finally{T.value=!1}}};return _e(()=>a.task,s=>{if(s){const d=s.templateId||s.planTemplateId||"";_.value={cronName:s.cronName||"",cronTime:s.cronTime||"",planDesc:s.planDesc||"",status:s.status??1,linkTemplate:!!d,templateId:d,planTemplateId:d}}else _.value={cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""}},{immediate:!0}),_e(()=>a.modelValue,s=>{s||(_.value={cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""})}),(s,d)=>(h(),ce(Ae,{to:"body"},[k(De,{name:"modal"},{default:xe(()=>{var E,X,L;return[s.modelValue?(h(),g("div",{key:0,class:"modal-overlay",onClick:I},[e("div",{class:"modal-container",onClick:d[8]||(d[8]=ie(()=>{},["stop"]))},[e("div",ka,[e("h3",null,i(s.$t("cronTask.taskDetail")),1),e("div",_a,[e("div",$a,[e("span",Pa,i(s.$t("cronTask.taskStatus")),1),e("label",Ca,[e("input",{type:"checkbox",checked:_.value.status===0,onChange:d[0]||(d[0]=O=>_.value.status=_.value.status===0?1:0)},null,40,Sa),d[9]||(d[9]=e("span",{class:"toggle-slider"},null,-1))])]),e("button",{class:"close-btn",onClick:d[1]||(d[1]=O=>s.$emit("update:modelValue",!1))},[k(l($),{icon:"carbon:close"})])])]),e("div",ya,[e("form",{onSubmit:ie(V,["prevent"]),class:"task-form"},[e("div",Ea,[e("label",wa,i(s.$t("cronTask.taskName")),1),re(e("input",{"onUpdate:modelValue":d[2]||(d[2]=O=>_.value.cronName=O),type:"text",class:"form-input",placeholder:s.$t("cronTask.taskNamePlaceholder"),required:""},null,8,Ta),[[be,_.value.cronName]])]),e("div",Ia,[e("label",Da,i(s.$t("cronTask.cronExpression")),1),re(e("input",{"onUpdate:modelValue":d[3]||(d[3]=O=>_.value.cronTime=O),type:"text",class:"form-input",placeholder:s.$t("cronTask.cronExpressionPlaceholder"),required:""},null,8,xa),[[be,_.value.cronTime]]),e("div",Ra,i(s.$t("cronTask.cronExpressionHelp")),1)]),e("div",Aa,[e("label",Ma,i(s.$t("cronTask.taskDescription")),1),re(e("textarea",{"onUpdate:modelValue":d[4]||(d[4]=O=>_.value.planDesc=O),class:"form-textarea",placeholder:s.$t("cronTask.taskDescriptionPlaceholder"),rows:"4",required:""},null,8,Na),[[be,_.value.planDesc]])]),e("div",Ua,[e("label",La,i(s.$t("cronTask.planTemplate")),1),e("div",Va,[e("button",{type:"button",class:te(["template-btn",_.value.linkTemplate?"active":""]),onClick:d[5]||(d[5]=O=>_.value.linkTemplate=!0)},[k(l($),{icon:"carbon:checkmark"}),Y(" "+i(s.$t("cronTask.linkTemplate")),1)],2),e("button",{type:"button",class:te(["template-btn",_.value.linkTemplate?"":"active"]),onClick:D},[k(l($),{icon:"carbon:close"}),Y(" "+i(s.$t("cronTask.noTemplate")),1)],2)]),_.value.linkTemplate?(h(),g("div",qa,[re(e("select",{"onUpdate:modelValue":d[6]||(d[6]=O=>_.value.templateId=O),class:"form-select"},[e("option",Fa,i(s.$t("cronTask.selectTemplate")),1),(h(!0),g(ge,null,fe(v.value,O=>(h(),g("option",{key:O.id,value:O.id},i(O.name),9,Oa))),128))],512),[[rt,_.value.templateId]]),e("div",Ba,i(s.$t("cronTask.templateHelpText")),1)])):B("",!0)]),(E=s.task)!=null&&E.createTime?(h(),g("div",Wa,[e("div",ja,[e("span",Ha,i(s.$t("cronTask.createTime"))+":",1),e("span",Ja,i(G(s.task.createTime)),1)])])):B("",!0),(X=s.task)!=null&&X.updateTime?(h(),g("div",za,[e("div",Ga,[e("span",Xa,i(s.$t("cronTask.updateTime"))+":",1),e("span",Ka,i(G(s.task.updateTime)),1)])])):B("",!0)],32)]),e("div",Qa,[e("button",{type:"button",class:"cancel-btn",onClick:d[7]||(d[7]=O=>s.$emit("update:modelValue",!1))},i(s.$t("common.cancel")),1),e("button",{type:"button",class:"save-btn",onClick:V,disabled:T.value},[T.value?(h(),ce(l($),{key:0,icon:"carbon:loading",class:"loading-icon"})):B("",!0),Y(" "+i((L=a.task)!=null&&L.id?s.$t("common.save"):s.$t("common.create")),1)],8,Ya)])])])):B("",!0)]}),_:1})]))}}),el=Se(Za,[["__scopeId","data-v-c9380237"]]),tl={class:"modal-header"},nl={class:"header-actions"},sl={class:"modal-content"},ol={key:0,class:"loading-container"},al={key:1,class:"empty-container"},ll={key:2,class:"task-list"},il=["onClick"],cl={class:"task-main"},rl={class:"task-info"},ul={class:"task-header"},dl={class:"task-name"},pl={class:"task-description"},hl={class:"task-time"},gl=["onClick"],ml=["onClick","disabled","title"],vl=["onClick","title"],fl={class:"dropdown-menu"},bl=["onClick"],kl=["onClick","disabled"],_l=["onClick","disabled"],$l={class:"confirm-header"},Pl={class:"confirm-content"},Cl={class:"confirm-actions"},Sl=["disabled"],yl={class:"confirm-header"},El={class:"confirm-content"},wl={class:"create-options"},Tl={class:"option-content"},Il={class:"option-title"},Dl={class:"option-desc"},xl={class:"option-content"},Rl={class:"option-title"},Al={class:"option-desc"},Ml={class:"confirm-actions"},Nl=Pe({__name:"index",props:{modelValue:{type:Boolean,required:!0}},emits:["update:modelValue"],setup(w,{emit:o}){const a=at(),r=lt(),T=gt(),{t:v}=Te(),_=w,x=o,I=R([]),D=R(!1),K=R(null),G=R(null),V=R(null),s=R(null),d=R(!1),E=R(null),X=R(!1),L=R(null),O=R(!1),q=u=>{u.target===u.currentTarget&&x("update:modelValue",!1)},Q=async()=>{D.value=!0;try{I.value=await Ee.getAllCronTasks()}catch(u){console.error("Failed to load cron tasks:",u),T.error(`Failed to load tasks: ${u instanceof Error?u.message:String(u)}`)}finally{D.value=!1}},me=async u=>{K.value=u;try{const S=I.value.find(c=>c.id===u);if(!S){console.error("Task not found:",u);return}x("update:modelValue",!1);const t=Date.now().toString();await a.push({name:"direct",params:{id:t}}),await new Promise(c=>setTimeout(c,100));const n=we.prepareTaskExecution(S);n.useTemplate&&n.planData?r.emitPlanExecutionRequested(n.planData):n.taskContent&&r.setTask(n.taskContent)}catch(S){console.error("Failed to execute task:",S),T.error(`Execution failed: ${S instanceof Error?S.message:String(S)}`)}finally{K.value=null}},ue=u=>{E.value={...u},d.value=!0,s.value=null},C=async u=>{try{await we.saveTask(u),await Q(),d.value=!1,T.success("Task saved successfully")}catch(S){console.error("Failed to save task:",S),T.error(`Save failed: ${S instanceof Error?S.message:String(S)}`)}},P=u=>{L.value=u,X.value=!0},F=async()=>{var u;if((u=L.value)!=null&&u.id){G.value=L.value.id;try{await we.deleteTask(L.value.id),await Q(),X.value=!1,L.value=null,T.success("Task deleted successfully")}catch(S){console.error("Failed to delete task:",S),T.error(`Delete failed: ${S instanceof Error?S.message:String(S)}`)}finally{G.value=null}}},A=()=>{X.value=!1,L.value=null},y=u=>{s.value=s.value===u?null:u},M=async u=>{if(u.id){V.value=u.id;try{await we.toggleTaskStatus(u),await Q(),s.value=null,T.success(`Task ${u.status===0?"disabled":"enabled"} successfully`)}catch(S){console.error("Failed to toggle task status:",S),T.error(`Status toggle failed: ${S instanceof Error?S.message:String(S)}`)}finally{V.value=null}}},ee=async u=>{try{await navigator.clipboard.writeText(u),T.success("Cron expression copied successfully")}catch(S){T.error(`Copy failed: ${S instanceof Error?S.message:String(S)}`)}},se=()=>{O.value=!0},ve=()=>{O.value=!1;try{x("update:modelValue",!1);const u=v("cronTask.template");r.setTaskToInput(u);const S=Date.now().toString();a.push({name:"direct",params:{id:S}})}catch(u){console.error("Error in createWithJmanus:",u),T.error(`Creation failed: ${u instanceof Error?u.message:String(u)}`)}},de=()=>{O.value=!1,E.value={cronName:"",cronTime:"",planDesc:"",status:0,planTemplateId:""},d.value=!0},b=()=>{O.value=!1},m=u=>{const S=u.target;!S.closest(".action-dropdown")&&!S.closest(".dropdown-menu")&&(s.value=null)};return Ce(()=>{document.addEventListener("click",m,!0)}),Ie(()=>{document.removeEventListener("click",m,!0)}),_e(()=>_.modelValue,u=>{u&&Q()}),(u,S)=>(h(),g(ge,null,[(h(),ce(Ae,{to:"body"},[k(De,{name:"modal"},{default:xe(()=>[w.modelValue?(h(),g("div",{key:0,class:"modal-overlay",onClick:q},[e("div",{class:"modal-container",onClick:S[3]||(S[3]=ie(()=>{},["stop"]))},[e("div",tl,[e("h3",null,i(u.$t("cronTask.title")),1),e("div",nl,[e("button",{class:"add-task-btn",onClick:[se,S[0]||(S[0]=ie(()=>{},["stop"]))]},[k(l($),{icon:"carbon:add"}),Y(" "+i(u.$t("cronTask.addTask")),1)]),e("button",{class:"close-btn",onClick:S[1]||(S[1]=t=>u.$emit("update:modelValue",!1))},[k(l($),{icon:"carbon:close"})])])]),e("div",sl,[D.value?(h(),g("div",ol,[k(l($),{icon:"carbon:loading",class:"loading-icon"}),e("span",null,i(u.$t("common.loading")),1)])):I.value.length===0?(h(),g("div",al,[k(l($),{icon:"carbon:time",class:"empty-icon"}),e("span",null,i(u.$t("cronTask.noTasks")),1)])):(h(),g("div",ll,[(h(!0),g(ge,null,fe(I.value,t=>(h(),g("div",{key:t.id||"",class:"task-item",onClick:n=>ue(t)},[e("div",cl,[e("div",rl,[e("div",ul,[e("div",dl,i(t.cronName),1),e("div",{class:te(["task-status-badge",t.status===0?"active":"inactive"])},[k(l($),{icon:t.status===0?"carbon:checkmark-filled":"carbon:pause-filled"},null,8,["icon"]),e("span",null,i(t.status===0?u.$t("cronTask.active"):u.$t("cronTask.inactive")),1)],2)]),e("div",pl,i(t.planDesc),1),e("div",hl,[k(l($),{icon:"carbon:time"}),e("span",{class:"cron-readable",style:{cursor:"pointer"},onClick:ie(n=>ee(t.cronTime),["stop"])},i(t.cronTime),9,gl)])])]),e("div",{class:"task-actions",onClick:S[2]||(S[2]=ie(()=>{},["stop"]))},[e("button",{class:"action-btn execute-btn",onClick:n=>me(t.id),disabled:K.value===t.id,title:u.$t("cronTask.executeOnce")},[k(l($),{icon:K.value===t.id?"carbon:loading":"carbon:play-filled"},null,8,["icon"]),Y(" "+i(u.$t("cronTask.executeOnce")),1)],8,ml),e("div",{class:te(["action-dropdown",{active:s.value===t.id}])},[e("button",{class:"action-btn dropdown-btn",onClick:n=>y(t.id),title:u.$t("cronTask.operations")},[k(l($),{icon:"carbon:overflow-menu-horizontal"}),Y(" "+i(u.$t("cronTask.operations")),1)],8,vl),re(e("div",fl,[e("button",{class:"dropdown-item edit-btn",onClick:n=>ue(t)},[k(l($),{icon:"carbon:edit"}),Y(" "+i(u.$t("cronTask.edit")),1)],8,bl),e("button",{class:"dropdown-item toggle-btn",onClick:n=>M(t),disabled:V.value===t.id},[k(l($),{icon:V.value===t.id?"carbon:loading":t.status===0?"carbon:pause-filled":"carbon:play-filled"},null,8,["icon"]),Y(" "+i(t.status===0?u.$t("cronTask.disable"):u.$t("cronTask.enable")),1)],8,kl),e("button",{class:"dropdown-item delete-btn",onClick:n=>P(t),disabled:G.value===t.id},[k(l($),{icon:G.value===t.id?"carbon:loading":"carbon:trash-can"},null,8,["icon"]),Y(" "+i(u.$t("cronTask.delete")),1)],8,_l)],512),[[ut,s.value===t.id]])],2)])],8,il))),128))]))])])])):B("",!0)]),_:1})])),k(el,{modelValue:d.value,"onUpdate:modelValue":S[4]||(S[4]=t=>d.value=t),task:E.value,onSave:C},null,8,["modelValue","task"]),(h(),ce(Ae,{to:"body"},[k(De,{name:"modal"},{default:xe(()=>{var t,n,c,p;return[X.value?(h(),g("div",{key:0,class:"modal-overlay",onClick:A},[e("div",{class:"confirm-modal",onClick:S[5]||(S[5]=ie(()=>{},["stop"]))},[e("div",$l,[k(l($),{icon:"carbon:warning",class:"warning-icon"}),e("h3",null,i(u.$t("cronTask.deleteConfirm")),1)]),e("div",Pl,[e("p",null,i(u.$t("cronTask.deleteConfirmMessage",{taskName:((t=L.value)==null?void 0:t.cronName)||((n=L.value)==null?void 0:n.planDesc)||""})),1)]),e("div",Cl,[e("button",{class:"confirm-btn cancel-btn",onClick:A},i(u.$t("common.cancel")),1),e("button",{class:"confirm-btn delete-btn",onClick:F,disabled:G.value===((c=L.value)==null?void 0:c.id)},[k(l($),{icon:G.value===((p=L.value)==null?void 0:p.id)?"carbon:loading":"carbon:trash-can"},null,8,["icon"]),Y(" "+i(u.$t("cronTask.delete")),1)],8,Sl)])])])):B("",!0)]}),_:1})])),(h(),ce(Ae,{to:"body"},[k(De,{name:"modal"},{default:xe(()=>[O.value?(h(),g("div",{key:0,class:"modal-overlay",onClick:b},[e("div",{class:"confirm-modal create-options-modal",onClick:S[6]||(S[6]=ie(()=>{},["stop"]))},[e("div",yl,[k(l($),{icon:"carbon:time",class:"create-icon"}),e("h3",null,i(u.$t("cronTask.createTask")),1)]),e("div",El,[e("p",null,i(u.$t("cronTask.selectCreateMethod")),1),e("div",wl,[e("button",{class:"create-option-btn jmanus-btn",onClick:ve},[k(l($),{icon:"carbon:ai-status"}),e("div",Tl,[e("span",Il,i(u.$t("cronTask.createWithJmanus")),1),e("span",Dl,i(u.$t("cronTask.createWithJmanusDesc")),1)])]),e("button",{class:"create-option-btn manual-btn",onClick:de},[k(l($),{icon:"carbon:edit"}),e("div",xl,[e("span",Rl,i(u.$t("cronTask.createManually")),1),e("span",Al,i(u.$t("cronTask.createManuallyDesc")),1)])])])]),e("div",Ml,[e("button",{class:"confirm-btn cancel-btn",onClick:b},i(u.$t("common.cancel")),1)])])])):B("",!0)]),_:1})]))],64))}}),Ul=Se(Nl,[["__scopeId","data-v-f31a9ce7"]]),Ll={class:"direct-page"},Vl={class:"direct-chat"},ql={class:"chat-header"},Fl={class:"header-actions"},Ol=["title"],Bl=["title"],Wl={class:"chat-content"},jl=["title"],Hl=Pe({__name:"index",setup(w){const o=dt(),a=at(),r=lt(),{t:T}=Te(),v=R(""),_=R(""),x=R(),I=R(),D=R(),K=R(!1),G=R(!1),V=R(null),s=R(!1),d=R(50),E=R(!1),X=R(0),L=R(0);Ce(()=>{if(console.log("[Direct] onMounted called"),console.log("[Direct] taskStore.currentTask:",r.currentTask),console.log("[Direct] taskStore.hasUnprocessedTask():",r.hasUnprocessedTask()),oe.setEventCallbacks({onPlanUpdate:m=>{console.log("[Direct] Plan update event received for rootPlanId:",m),ue(m)&&(console.log("[Direct] Processing plan update for current rootPlanId:",m),I.value&&typeof I.value.handlePlanUpdate=="function"?(console.log("[Direct] Calling chatRef.handlePlanUpdate with rootPlanId:",m),I.value.handlePlanUpdate(m)):console.warn("[Direct] chatRef.handlePlanUpdate method not available"),x.value&&typeof x.value.updateDisplayedPlanProgress=="function"?(console.log("[Direct] Calling rightPanelRef.updateDisplayedPlanProgress with rootPlanId:",m),x.value.updateDisplayedPlanProgress(m)):console.warn("[Direct] rightPanelRef.updateDisplayedPlanProgress method not available"))},onPlanCompleted:m=>{if(console.log("[Direct] Plan completed event received for rootPlanId:",m),!!ue(m)){if(console.log("[Direct] Processing plan completion for current rootPlanId:",m),I.value&&typeof I.value.handlePlanCompleted=="function"){const u=oe.getCachedPlanRecord(m);console.log("[Direct] Calling chatRef.handlePlanCompleted with details:",u),I.value.handlePlanCompleted(u??{planId:m})}else console.warn("[Direct] chatRef.handlePlanCompleted method not available");V.value=null,console.log("[Direct] Cleared currentRootPlanId after plan completion")}},onDialogRoundStart:m=>{console.log("[Direct] Dialog round start event received for rootPlanId:",m),V.value=m,console.log("[Direct] Set currentRootPlanId to:",m),I.value&&typeof I.value.handleDialogRoundStart=="function"?(console.log("[Direct] Calling chatRef.handleDialogRoundStart with planId:",m),I.value.handleDialogRoundStart(m)):console.warn("[Direct] chatRef.handleDialogRoundStart method not available")},onChatInputClear:()=>{console.log("[Direct] Chat input clear event received"),P()},onChatInputUpdateState:m=>{if(console.log("[Direct] Chat input update state event received for rootPlanId:",m),!ue(m,!0))return;const u=oe.getCachedUIState(m);u&&A(u.enabled,u.placeholder)}}),console.log("[Direct] Event callbacks registered to planExecutionManager"),f.loadPlanTemplateList(),r.hasUnprocessedTask()&&r.currentTask){const m=r.currentTask.prompt;console.log("[Direct] Found unprocessed task from store:",m),r.markTaskAsProcessed(),ne(()=>{I.value&&typeof I.value.handleSendMessage=="function"?(console.log("[Direct] Directly executing task via chatRef.handleSendMessage:",m),I.value.handleSendMessage(m)):(console.warn("[Direct] chatRef.handleSendMessage method not available, falling back to prompt"),v.value=m)})}else{const m=r.getAndClearTaskToInput();m?(_.value=m,console.log("[Direct] Setting inputOnlyContent for input only:",_.value)):(v.value=o.query.prompt||"",console.log("[Direct] Received task from URL:",v.value),console.log("[Direct] No unprocessed task in store"))}const b=localStorage.getItem("directPanelWidth");b&&(d.value=parseFloat(b)),console.log("[Direct] Final prompt value:",v.value),_.value&&ne(()=>{D.value&&typeof D.value.setInputValue=="function"&&(D.value.setInputValue(_.value),console.log("[Direct] Set input value:",_.value),_.value="")}),window.addEventListener("plan-execution-requested",m=>{console.log("[DirectView] Received plan-execution-requested event:",m.detail),de(m.detail)})}),_e(()=>r.currentTask,b=>{if(console.log("[Direct] Watch taskStore.currentTask triggered, newTask:",b),b&&!b.processed){const m=b.prompt;r.markTaskAsProcessed(),console.log("[Direct] Received new task from store:",m),ne(()=>{I.value&&typeof I.value.handleSendMessage=="function"?(console.log("[Direct] Directly executing new task via chatRef.handleSendMessage:",m),I.value.handleSendMessage(m)):console.warn("[Direct] chatRef.handleSendMessage method not available for new task")})}else console.log("[Direct] Task is null or already processed, ignoring")},{immediate:!1}),_e(()=>v.value,(b,m)=>{console.log("[Direct] prompt value changed from:",m,"to:",b)},{immediate:!1}),_e(()=>r.taskToInput,b=>{console.log("[Direct] Watch taskStore.taskToInput triggered, newTaskToInput:",b),b&&b.trim()&&(console.log("[Direct] Setting input value from taskToInput:",b),ne(()=>{D.value&&typeof D.value.setInputValue=="function"&&(D.value.setInputValue(b.trim()),console.log("[Direct] Input value set from taskToInput watch:",b.trim()),r.getAndClearTaskToInput())}))},{immediate:!1}),Ie(()=>{console.log("[Direct] onUnmounted called, cleaning up resources"),V.value=null,oe.cleanup(),document.removeEventListener("mousemove",q),document.removeEventListener("mouseup",Q),window.removeEventListener("plan-execution-requested",b=>{de(b.detail)})});const O=b=>{E.value=!0,X.value=b.clientX,L.value=d.value,document.addEventListener("mousemove",q),document.addEventListener("mouseup",Q),document.body.style.cursor="col-resize",document.body.style.userSelect="none",b.preventDefault()},q=b=>{if(!E.value)return;const m=window.innerWidth,S=(b.clientX-X.value)/m*100;let t=L.value+S;t=Math.max(20,Math.min(80,t)),d.value=t},Q=()=>{E.value=!1,document.removeEventListener("mousemove",q),document.removeEventListener("mouseup",Q),document.body.style.cursor="",document.body.style.userSelect="",localStorage.setItem("directPanelWidth",d.value.toString())},me=()=>{d.value=50,localStorage.setItem("directPanelWidth","50")},ue=(b,m=!1)=>!V.value||b===V.value||m&&(b==="ui-state"||b==="error")?!0:(console.log("[Direct] Ignoring event for non-current rootPlanId:",b,"current:",V.value),!1),C=b=>{console.log("[DirectView] Send message from input:",b),I.value&&typeof I.value.handleSendMessage=="function"?(console.log("[DirectView] Calling chatRef.handleSendMessage:",b),I.value.handleSendMessage(b)):console.warn("[DirectView] chatRef.handleSendMessage method not available")},P=()=>{console.log("[DirectView] Input cleared"),D.value&&typeof D.value.clear=="function"&&D.value.clear()},F=()=>{console.log("[DirectView] Input focused")},A=(b,m)=>{console.log("[DirectView] Input state updated:",b,m),G.value=!b},y=(b,m)=>{console.log("[DirectView] Step selected:",b,m),x.value&&typeof x.value.handleStepSelected=="function"?(console.log("[DirectView] Forwarding step selection to right panel:",b,m),x.value.handleStepSelected(b,m)):console.warn("[DirectView] rightPanelRef.handleStepSelected method not available")},M=(b,m,u,S)=>{console.log("[DirectView] Sub plan step selected:",{parentPlanId:b,subPlanId:m,stepIndex:u,subStepIndex:S}),x.value&&typeof x.value.handleSubPlanStepSelected=="function"?(console.log("[DirectView] Forwarding sub plan step selection to right panel:",{parentPlanId:b,subPlanId:m,stepIndex:u,subStepIndex:S}),x.value.handleSubPlanStepSelected(b,m,u,S)):console.warn("[DirectView] rightPanelRef.handleSubPlanStepSelected method not available")},ee=()=>{console.log("[DirectView] Plan mode button clicked"),f.toggleSidebar(),console.log("[DirectView] Sidebar toggled, isCollapsed:",f.isCollapsed)},se=()=>{a.push("/home")},ve=()=>{a.push("/configs")},de=async b=>{var u,S,t,n;if(console.log("[DirectView] Plan execution requested:",b),K.value){console.log("[DirectView] Plan execution already in progress, ignoring request");return}K.value=!0;let m=!1;I.value&&typeof I.value.addMessage=="function"?(console.log("[DirectView] Calling chatRef.addMessage for plan execution:",b.title),I.value.addMessage("user",b.title),m=!0):console.warn("[DirectView] chatRef.addMessage method not available");try{const c=((u=b.planData)==null?void 0:u.planTemplateId)||((S=b.planData)==null?void 0:S.id)||((t=b.planData)==null?void 0:t.planId);if(!c)throw new Error("没有找到计划模板ID");console.log("[Direct] Executing plan with templateId:",c,"params:",b.params),console.log("[Direct] About to call PlanActApiService.executePlan");let p;if((n=b.params)!=null&&n.trim()?(console.log("[Direct] Calling executePlan with params:",b.params.trim()),p=await Me.executePlan(c,b.params.trim())):(console.log("[Direct] Calling executePlan without params"),p=await Me.executePlan(c)),console.log("[Direct] Plan execution API response:",p),p.planId)console.log("[Direct] Got planId from response:",p.planId,"starting plan execution"),V.value=p.planId,console.log("[Direct] Set currentRootPlanId to:",p.planId),console.log("[Direct] Delegating plan execution to planExecutionManager"),oe.handlePlanExecutionRequested(p.planId,b.title);else throw console.error("[Direct] No planId in response:",p),new Error("执行计划失败：未返回有效的计划ID")}catch(c){console.error("[Direct] Plan execution failed:",c),console.error("[Direct] Error details:",{message:c.message,stack:c.stack}),V.value=null,I.value&&typeof I.value.addMessage=="function"?(console.log("[Direct] Adding error messages to chat"),m||I.value.addMessage("user",b.title),I.value.addMessage("assistant",`执行计划失败: ${c.message||"未知错误"}`,{thinking:void 0})):(console.error("[Direct] Chat ref not available, showing alert"),alert(`执行计划失败: ${c.message||"未知错误"}`))}finally{console.log("[Direct] Plan execution finished, resetting isExecutingPlan flag"),K.value=!1}};return(b,m)=>(h(),g("div",Ll,[e("div",Vl,[k(mn,{onPlanExecutionRequested:de}),e("div",{class:"left-panel",style:Ne({width:d.value+"%"})},[e("div",ql,[e("button",{class:"back-button",onClick:se},[k(l($),{icon:"carbon:arrow-left"})]),e("h2",null,i(b.$t("conversation")),1),e("div",Fl,[k(ht),e("button",{class:"config-button",onClick:ve,title:b.$t("direct.configuration")},[k(l($),{icon:"carbon:settings-adjust",width:"20"})],8,Ol),e("button",{class:"cron-task-btn",onClick:m[0]||(m[0]=u=>s.value=!0),title:b.$t("cronTask.title")},[k(l($),{icon:"carbon:alarm",width:"20"})],8,Bl)])]),e("div",Wl,[k(ua,{ref_key:"chatRef",ref:I,mode:"direct","initial-prompt":v.value||"",onStepSelected:y,onSubPlanStepSelected:M},null,8,["initial-prompt"])]),(h(),ce(ba,{key:b.$i18n.locale,ref_key:"inputRef",ref:D,disabled:G.value,placeholder:G.value?l(T)("input.waiting"):l(T)("input.placeholder"),"initial-value":v.value,onSend:C,onClear:P,onFocus:F,onUpdateState:A,onPlanModeClicked:ee},null,8,["disabled","placeholder","initial-value"]))],4),e("div",{class:"panel-resizer",onMousedown:O,onDblclick:me,title:b.$t("direct.panelResizeHint")},m[2]||(m[2]=[e("div",{class:"resizer-line"},null,-1)]),40,jl),k(xs,{ref_key:"rightPanelRef",ref:x,style:Ne({width:100-d.value+"%"})},null,8,["style"])]),k(Ul,{modelValue:s.value,"onUpdate:modelValue":m[1]||(m[1]=u=>s.value=u)},null,8,["modelValue"])]))}}),ei=Se(Hl,[["__scopeId","data-v-696a3f65"]]);export{ei as default};
