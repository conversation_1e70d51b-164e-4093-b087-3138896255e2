import{d as _,r as c,s as k,b as m,g as p,k as h,a as b,f as C,e as x,x as T,t as w,n as y,T as B,C as g,P as N}from"./index-BaYfAMDB.js";import{I as V}from"./iconify-Cd9Ozm-0.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const z=_({__name:"Toast",setup(a,{expose:e}){const o=c(!1),n=c(""),r=c("success"),l=c("carbon:checkmark"),u=c(3e3),d=(i,t="success",f=3e3)=>{n.value=i,r.value=t,l.value=t==="success"?"carbon:checkmark":"carbon:error",u.value=f,o.value=!0,setTimeout(()=>{o.value=!1},u.value)},v=()=>{o.value=!1};return e({show:d}),(i,t)=>(m(),k(g,{to:"body"},[p(B,{name:"slide"},{default:h(()=>[o.value?(m(),b("div",{key:0,class:y(["toast",`toast--${r.value}`]),onClick:v},[p(T(V),{icon:l.value,class:"toast-icon"},null,8,["icon"]),x("span",null,w(n.value),1)],2)):C("",!0)]),_:1})]))}}),A=E(z,[["__scopeId","data-v-581895ae"]]);let s=null;const $=()=>{if(!s){const a=N(A),e=document.createElement("div");document.body.appendChild(e),s=a.mount(e)}return{success:(a,e)=>{s==null||s.show(a,"success",e)},error:(a,e)=>{s==null||s.show(a,"error",e)}}};export{$ as u};
