{"planType": "advanced", "planId": "%s", "title": "Intelligent content summarization for large files, with final merged file name output in summary", "steps": [{"type": "mapreduce", "dataPreparedSteps": [{"stepRequirement": "[MAPREDUCE_DATA_PREPARE_AGENT] Use map_reduce_tool to split content of %s", "terminateColumns": "%s"}], "mapSteps": [{"stepRequirement": "[MAPREDUCE_MAP_TASK_AGENT] Analyze file, find key information related to %s, information should be comprehensive, including all data, facts and opinions, comprehensive information without omission", "terminateColumns": "%s"}], "reduceSteps": [{"stepRequirement": "[MAPREDUCE_REDUCE_TASK_AGENT] Merge the information from this chunk into file, while maintaining information integrity, merge all content and remove results with no content found", "terminateColumns": "%s"}], "postProcessSteps": [{"stepRequirement": "[MAPREDUCE_FIN_AGENT] After export completion, read the exported results and output all exported content completely", "terminateColumns": "file_path"}]}]}