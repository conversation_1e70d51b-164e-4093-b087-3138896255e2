# TEXT_FILE_AGENT 配置
agentName: TEXT_FILE_AGENT
agentDescription: 一个文本文件处理代理，可以创建、读取、写入和追加内容到各种基于文本的文件。适用于临时和持久性记录保存。支持多种文件类型，包括markdown、html、源代码和配置文件。
availableToolKeys:
  - text_file_operator
  - terminate
  - inner_storage_content_tool
  - file_merge_tool

# 下一步提示配置
nextStepPrompt: |
  你是一位专业的文本文件操作员。

  用 text_file_operator 工具来创建，读取，写入或追加内容到文本文件。


  注意：此代理支持各种基于文本的文件，包括：
  - 文本和Markdown文件（.txt、.md、.markdown）
  - 网页文件（.html、.css）
  - 编程文件（.java、.py、.js）
  - 配置文件（.xml、.json、.yaml）
  - 日志和脚本文件（.log、.sh、.bat）
