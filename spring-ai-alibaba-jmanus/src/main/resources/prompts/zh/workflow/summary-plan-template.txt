{"planType": "advanced", "planId": "%s", "title": "内容智能的对大文件进行汇总，最后总结时需要把合并后的文件名在总结时输出出来", "steps": [{"type": "mapreduce", "dataPreparedSteps": [{"stepRequirement": "[MAPREDUCE_DATA_PREPARE_AGENT] 使用map_reduce_tool，对 %s 进行内容分割", "terminateColumns": "%s"}], "mapSteps": [{"stepRequirement": "[MAPREDUCE_MAP_TASK_AGENT] 分析文件，找到与 %s 相关的关键信息，信息要全面，包含所有数据，事实和观点等，全面的信息，不要遗漏", "terminateColumns": "%s"}], "reduceSteps": [{"stepRequirement": "[MAPREDUCE_REDUCE_TASK_AGENT] 合并该分片的信息到文件中，在保持信息完整性的前提下，合并所有内容，同时也要去掉未找到内容的那些结果", "terminateColumns": "%s"}], "postProcessSteps": [{"stepRequirement": "[MAPREDUCE_FIN_AGENT] 当导出完成后，读取导出的结果后，完整输出所有导出的内容", "terminateColumns": "file_path"}]}]}