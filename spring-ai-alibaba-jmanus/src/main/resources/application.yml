server:
  port: 18080
spring:
  config:
    import: classpath:application-database-tool.yml
  application:
    name: spring-ai-alibaba-openmanus
  profiles:
    active: h2
  main:
    allow-circular-references: true
    lazy-initialization: false
  aop:
    proxy-target-class: true
  # Hikari Configuration
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      pool-name: Spring-AI-Alibaba-JManus-${spring.profiles.active}-Pool
      connection-test-query: SELECT 1
      validation-timeout: 5000
      leak-detection-threshold: 60000
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true

logging:
  file:
    name: ./logs/info.log
  level:
    root: INFO

namespace:
  value: default

agent:
  # Initialize save agent on startup
  init: true
  # Serialize: fastjson | jackson
  serialize: jackson
