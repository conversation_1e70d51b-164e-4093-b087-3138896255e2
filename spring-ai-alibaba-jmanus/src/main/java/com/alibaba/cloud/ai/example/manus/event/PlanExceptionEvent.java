/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.example.manus.event;

/**
 * <AUTHOR>
 * @time 2025/7/30
 * @desc jmanus计划执行异常事件
 */
public class PlanExceptionEvent implements JmanusEvent {

	private String planId;

	private Throwable throwable;

	private long createTime;

	public PlanExceptionEvent(String planId, Throwable throwable) {
		this.planId = planId;
		this.throwable = throwable;
		this.createTime = System.currentTimeMillis();
	}

	public String getPlanId() {
		return planId;
	}

	public Throwable getThrowable() {
		return throwable;
	}

	public long getCreateTime() {
		return createTime;
	}

}
