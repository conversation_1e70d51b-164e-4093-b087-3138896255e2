/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.example.manus.planning.model.vo.mapreduce;

import com.alibaba.cloud.ai.example.manus.planning.model.vo.ExecutionStep;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * 执行节点抽象基类，提供公共的默认实现
 */
public abstract class AbstractExecutionNode implements ExecutionNode {

	@JsonIgnore
	protected MapReduceStepType type;

	protected AbstractExecutionNode(MapReduceStepType type) {
		this.type = type;
	}

	@Override
	@JsonIgnore
	public MapReduceStepType getType() {
		return type;
	}

	@Override
	@JsonIgnore
	public int getTotalStepCount() {
		List<ExecutionStep> allSteps = getAllSteps();
		return allSteps != null ? allSteps.size() : 0;
	}

	@Override
	public String toString() {
		return getNodeInStr();
	}

}
