/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.example.manus.planning.model.vo;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.alibaba.cloud.ai.example.manus.planning.model.vo.mapreduce.MapReduceExecutionPlan;

/**
 * General interface for execution plans, defines the basic operations shared by all plan
 * types.
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "planType",
		defaultImpl = ExecutionPlan.class // Default implementation is ExecutionPlan
)
@JsonSubTypes({ @JsonSubTypes.Type(value = ExecutionPlan.class, name = "simple"),
		@JsonSubTypes.Type(value = MapReduceExecutionPlan.class, name = "advanced") })
public interface PlanInterface {

	/**
	 * Get the root plan ID (rootPlanId) of the plan. rootPlanId means the ID of the
	 * outermost/root parent plan for the entire execution plan. In both main and
	 * sub-plans, rootPlanId always points to the top-level parent plan.
	 * @return The root plan ID
	 */
	String getRootPlanId();

	/**
	 * Set the root plan ID (rootPlanId) for the plan.
	 * @param rootPlanId The root plan ID
	 */
	void setRootPlanId(String rootPlanId);

	/**
	 * Get the current plan ID (currentPlanId). currentPlanId may be equal to rootPlanId
	 * (for the main plan), or may be a sub-plan ID generated by the parent plan.
	 * @return The current plan ID
	 */
	String getCurrentPlanId();

	/**
	 * Set the current plan ID (currentPlanId).
	 * @param currentPlanId The current plan ID
	 */
	void setCurrentPlanId(String currentPlanId);

	/**
	 * Get the plan type.
	 * @return The plan type
	 */
	String getPlanType();

	/**
	 * Set the plan type.
	 * @param planType The plan type
	 */
	void setPlanType(String planType);

	/**
	 * Get the plan title.
	 * @return The plan title
	 */
	String getTitle();

	/**
	 * Set the plan title.
	 * @param title The plan title
	 */
	void setTitle(String title);

	/**
	 * Get the planning thinking process.
	 * @return The planning thinking process
	 */
	String getPlanningThinking();

	/**
	 * Set the planning thinking process.
	 * @param planningThinking The planning thinking process
	 */
	void setPlanningThinking(String planningThinking);

	/**
	 * Get the execution parameters.
	 * @return The execution parameters
	 */
	String getExecutionParams();

	/**
	 * Set the execution parameters.
	 * @param executionParams The execution parameters
	 */
	void setExecutionParams(String executionParams);

	/**
	 * Get the flat list of all execution steps.
	 * @return All execution steps
	 */
	List<ExecutionStep> getAllSteps();

	/**
	 * Get the total number of steps.
	 * @return The total number of steps
	 */
	int getTotalStepCount();

	/**
	 * Get the user request.
	 * @return The user request
	 */
	public String getUserRequest();

	/**
	 * Set the user request.
	 * @param userRequest The user request
	 */
	void setUserRequest(String userRequest);

	/**
	 * Add an execution step.
	 * @param step The execution step
	 */
	void addStep(ExecutionStep step);

	/**
	 * Remove an execution step.
	 * @param step The execution step
	 */
	void removeStep(ExecutionStep step);

	/**
	 * Check if the plan is empty.
	 * @return true if the plan is empty
	 */
	boolean isEmpty();

	/**
	 * Clear the plan content.
	 */
	void clear();

	/**
	 * Get the string format of the plan execution state.
	 * @param onlyCompletedAndFirstInProgress If true, only output all completed steps and
	 * the first in-progress step
	 * @return The plan execution state string
	 */
	String getPlanExecutionStateStringFormat(boolean onlyCompletedAndFirstInProgress);

	public void setPlanId(String planId);

	public String getPlanId();

	/**
	 * Whether it's direct feedback mode. When true, skip complex plan execution and use
	 * LLM to give response directly
	 * @return Return true if it's direct feedback mode
	 */
	boolean isDirectResponse();

	/**
	 * Set whether it's direct feedback mode
	 * @param directResponse Direct feedback mode flag
	 */
	void setDirectResponse(boolean directResponse);

	/**
	 * Update the indices of all steps, starting from 0.
	 */
	default void updateStepIndices() {
		List<ExecutionStep> allSteps = getAllSteps();
		for (int i = 0; i < allSteps.size(); i++) {
			allSteps.get(i).setStepIndex(i);
		}
	}

}
