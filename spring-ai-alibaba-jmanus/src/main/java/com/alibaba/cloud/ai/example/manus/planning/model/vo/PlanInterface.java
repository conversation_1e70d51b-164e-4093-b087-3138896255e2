/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.example.manus.planning.model.vo;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.alibaba.cloud.ai.example.manus.planning.model.vo.mapreduce.MapReduceExecutionPlan;

/**
 * 执行计划通用接口，定义了所有执行计划类型共同的基本操作。 General interface for execution plans, defines the basic
 * operations shared by all plan types.
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "planType",
		defaultImpl = ExecutionPlan.class // 默认实现为 ExecutionPlan
)
@JsonSubTypes({ @JsonSubTypes.Type(value = ExecutionPlan.class, name = "simple"),
		@JsonSubTypes.Type(value = MapReduceExecutionPlan.class, name = "advanced") })
public interface PlanInterface {

	/**
	 * 获取计划的最外层父计划ID（rootPlanId）。 rootPlanId means the ID of the outermost/root parent
	 * plan for the entire execution plan. 在主计划和所有子计划中，rootPlanId 都指向最顶层的父计划。 In both main
	 * and sub-plans, rootPlanId always points to the top-level parent plan.
	 * @return 计划的最外层父计划ID / The root plan ID
	 */
	String getRootPlanId();

	/**
	 * 设置计划的最外层父计划ID（rootPlanId）。 Set the root plan ID (rootPlanId) for the plan.
	 * @param rootPlanId 计划的最外层父计划ID / The root plan ID
	 */
	void setRootPlanId(String rootPlanId);

	/**
	 * 获取当前计划ID（currentPlanId）。 currentPlanId 可能等于 rootPlanId（主计划），也可能等于由父计划产生的子计划ID。
	 * currentPlanId may be equal to rootPlanId (for the main plan), or may be a sub-plan
	 * ID generated by the parent plan.
	 * @return 当前计划ID / The current plan ID
	 */
	String getCurrentPlanId();

	/**
	 * 设置当前计划ID（currentPlanId）。 Set the current plan ID (currentPlanId).
	 * @param currentPlanId 当前计划ID / The current plan ID
	 */
	void setCurrentPlanId(String currentPlanId);

	/**
	 * 获取计划类型。 Get the plan type.
	 * @return 计划类型 / The plan type
	 */
	String getPlanType();

	/**
	 * 设置计划类型。 Set the plan type.
	 * @param planType 计划类型 / The plan type
	 */
	void setPlanType(String planType);

	/**
	 * 获取计划标题。 Get the plan title.
	 * @return 计划标题 / The plan title
	 */
	String getTitle();

	/**
	 * 设置计划标题。 Set the plan title.
	 * @param title 计划标题 / The plan title
	 */
	void setTitle(String title);

	/**
	 * 获取规划思考过程。 Get the planning thinking process.
	 * @return 规划思考过程 / The planning thinking process
	 */
	String getPlanningThinking();

	/**
	 * 设置规划思考过程。 Set the planning thinking process.
	 * @param planningThinking 规划思考过程 / The planning thinking process
	 */
	void setPlanningThinking(String planningThinking);

	/**
	 * 获取执行参数。 Get the execution parameters.
	 * @return 执行参数 / The execution parameters
	 */
	String getExecutionParams();

	/**
	 * 设置执行参数。 Set the execution parameters.
	 * @param executionParams 执行参数 / The execution parameters
	 */
	void setExecutionParams(String executionParams);

	/**
	 * 获取所有执行步骤的扁平列表。 Get the flat list of all execution steps.
	 * @return 所有执行步骤 / All execution steps
	 */
	List<ExecutionStep> getAllSteps();

	/**
	 * 获取总步骤数量。 Get the total number of steps.
	 * @return 总步骤数 / The total number of steps
	 */
	int getTotalStepCount();

	/**
	 * 获取用户请求。 Get the user request.
	 * @return 用户请求 / The user request
	 */
	public String getUserRequest();

	/**
	 * 设置用户请求。 Set the user request.
	 * @param userRequest 用户请求 / The user request
	 */
	void setUserRequest(String userRequest);

	/**
	 * 添加执行步骤。 Add an execution step.
	 * @param step 执行步骤 / The execution step
	 */
	void addStep(ExecutionStep step);

	/**
	 * 移除执行步骤。 Remove an execution step.
	 * @param step 执行步骤 / The execution step
	 */
	void removeStep(ExecutionStep step);

	/**
	 * 检查计划是否为空。 Check if the plan is empty.
	 * @return 如果计划为空则返回true / true if the plan is empty
	 */
	boolean isEmpty();

	/**
	 * 清空计划内容。 Clear the plan content.
	 */
	void clear();

	/**
	 * 获取计划执行状态的字符串格式。 Get the string format of the plan execution state.
	 * @param onlyCompletedAndFirstInProgress 当为true时，只输出所有已完成的步骤和第一个进行中的步骤 / If true,
	 * only output all completed steps and the first in-progress step
	 * @return 计划状态字符串 / The plan execution state string
	 */
	String getPlanExecutionStateStringFormat(boolean onlyCompletedAndFirstInProgress);

	public void setPlanId(String planId);

	public String getPlanId();

	/**
	 * 是否为直接反馈模式 当为true时，跳过复杂的计划执行，直接使用LLM给出响应
	 * @return 如果是直接反馈模式则返回true
	 */
	boolean isDirectResponse();

	/**
	 * 设置是否为直接反馈模式
	 * @param directResponse 直接反馈模式标志
	 */
	void setDirectResponse(boolean directResponse);

	/**
	 * 更新所有步骤的索引，从0开始递增。 Update the indices of all steps, starting from 0.
	 */
	default void updateStepIndices() {
		List<ExecutionStep> allSteps = getAllSteps();
		for (int i = 0; i < allSteps.size(); i++) {
			allSteps.get(i).setStepIndex(i);
		}
	}

}
