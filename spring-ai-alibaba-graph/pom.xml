<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright 2025-2026 the original author or authors.

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

        https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.alibaba.cloud.ai</groupId>
        <artifactId>spring-ai-alibaba</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>spring-ai-alibaba-graph</artifactId>
    <packaging>pom</packaging>
    <name>Spring AI Alibaba Graph</name>
    <url>https://github.com/alibaba/spring-ai-alibaba</url>

    <modules>
        <module>spring-ai-alibaba-graph-core</module>
        <module>spring-ai-alibaba-graph-studio</module>
        <module>spring-ai-alibaba-graph-example</module>
    </modules>
    <scm>
        <connection>git://github.com/alibaba/spring-ai-alibaba.git</connection>
        <developerConnection>**************:alibaba/spring-ai-alibaba.git</developerConnection>
        <url>https://github.com/alibaba/spring-ai-alibaba</url>
    </scm>

</project>
