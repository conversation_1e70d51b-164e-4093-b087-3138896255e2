/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.service.dsl;

import java.util.Arrays;
import java.util.Optional;

public enum DSLDialectType {

	DIFY("dify", ".yml"),

	CUSTOM("custom", ".yml");

	private String value;

	private String fileExtension;

	public String value() {
		return value;
	}

	public String fileExtension() {
		return fileExtension;
	}

	public static Optional<DSLDialectType> fromValue(String value) {
		return Arrays.stream(DSLDialectType.values()).filter(t -> t.value.equals(value)).findFirst();
	}

	DSLDialectType(String value, String fileExtension) {
		this.value = value;
		this.fileExtension = fileExtension;
	}

}
