/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.service.generator;

import io.spring.initializr.generator.project.contributor.ProjectContributor;

import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

public class GraphProjectContributor implements ProjectContributor {

	private GraphProjectDescription projectDescription;

	private List<ProjectGenerator> projectGenerators;

	public GraphProjectContributor(GraphProjectDescription projectDescription,
			List<ProjectGenerator> projectGenerators) {
		this.projectDescription = projectDescription;
		this.projectGenerators = projectGenerators;
	}

	@Override
	public void contribute(Path projectRoot) throws IOException {
		for (ProjectGenerator generator : projectGenerators) {
			if (generator.supportAppMode(projectDescription.getAppMode())) {
				generator.generate(projectDescription, projectRoot);
				return;
			}
		}
	}

}
