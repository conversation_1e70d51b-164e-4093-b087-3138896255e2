/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.controller;

import com.alibaba.cloud.ai.api.RunnerAPI;
import com.alibaba.cloud.ai.saver.AppSaver;
import com.alibaba.cloud.ai.service.runner.Runner;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("graph-studio/api/run")
public class RunnerController implements RunnerAPI {

	private final List<Runner> runners;

	private final AppSaver appSaver;

	public RunnerController(List<Runner> runners, AppSaver appSaver) {
		this.runners = runners;
		this.appSaver = appSaver;
	}

	@Override
	public Runner getRunner(String type) {
		return runners.stream().filter(r -> r.support(type)).findFirst().orElse(null);
	}

	@Override
	public AppSaver getAppSaver() {
		return appSaver;
	}

}
