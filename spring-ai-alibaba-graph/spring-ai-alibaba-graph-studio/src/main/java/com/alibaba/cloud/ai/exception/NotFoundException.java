/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.exception;

import com.alibaba.cloud.ai.common.ReturnCode;

public class NotFoundException extends RuntimeException {

	private int code;

	private String msg;

	public int getCode() {
		return code;
	}

	public NotFoundException setCode(int code) {
		this.code = code;
		return this;
	}

	public String getMsg() {
		return msg;
	}

	public NotFoundException setMsg(String msg) {
		this.msg = msg;
		return this;
	}

	public NotFoundException() {
		this.code = ReturnCode.RC404.getCode();
		this.msg = ReturnCode.RC404.getMsg();
	}

	public NotFoundException(String msg) {
		this.code = ReturnCode.RC404.getCode();
		this.msg = msg;
	}

}
