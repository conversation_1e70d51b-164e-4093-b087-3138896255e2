/*
 * Copyright 2024-2026 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

///
/// Copyright 2024-2025 the original author or authors.
///
/// Licensed under the Apache License, Version 2.0 (the "License");
/// you may not use this file except in compliance with the License.
/// You may obtain a copy of the License at
///
///      https://www.apache.org/licenses/LICENSE-2.0
///
/// Unless required by applicable law or agreed to in writing, software
/// distributed under the License is distributed on an "AS IS" BASIS,
/// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
/// See the License for the specific language governing permissions and
/// limitations under the License.
///

/*
 * Copyright 2024-2026 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { DEFAULT_NAME } from '@/constants';

export default {
  site: {
    title: DEFAULT_NAME,
  },
  router: {
    home: 'Home',
    agent: 'Agent',
    chatbot: 'Chatbot',
    workspace: 'Workspace',
    projects: 'Projects',
    graph: 'Graph',
  },
  page: {
    graph: {
      sn: 'SN',
      graphName: 'GraphName',
      version: 'Version',
      graphDesc: 'GraphDesc',
      createTime: 'CreateTime',
      updateTime: 'UpdateTime',
      addNew: 'AddNew',
      genCode: 'GenCode',
      genProject: 'GenProject',
      option: 'Option',
      delete: 'Delete',
      editMeta: 'EditMeta',
      design: 'Design',
      search: 'Search',
    },
    chatbot: {
      sn: 'SN',
      chatbotName: 'Chatbot Name',
      version: 'Version',
      chatbotDesc: 'Chatbot Description',
      createTime: 'Create Time',
      updateTime: 'Update Time',
      addNew: 'Add New',
      genCode: 'Generate Code',
      genProject: 'Generate Project',
      option: 'Option',
      delete: 'Delete',
      editMeta: 'Edit Meta',
      design: 'Design',
      search: 'Search',
    },
    agent: {
      sn: 'SN',
      agentName: 'Agent Name',
      version: 'Version',
      agentDesc: 'Agent Description',
      createTime: 'Create Time',
      updateTime: 'Update Time',
      addNew: 'Add New',
      genCode: 'Generate Code',
      genProject: 'Generate Project',
      option: 'Option',
      delete: 'Delete',
      editMeta: 'Edit Meta',
      design: 'Design',
      search: 'Search',
    },
  },
};
