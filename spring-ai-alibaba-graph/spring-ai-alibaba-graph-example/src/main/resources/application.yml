#
# Copyright 2024-2025 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
server:
  port: 18080
spring:
  application:
    name: spring-ai-alibaba-graph-example
  ai:
    mcp:
      server:
        name: my-weather-server
        version: 0.0.1
        type: ASYNC # Recommended for reactive applications
        # Configure the root path for sse, default is /sse
        # The final path below will be ip:port/sse/mcp
        sse-endpoint: /sse
        sse-message-endpoint: /mcp
        capabilities:
          tool: true
          resource: true
          prompt: true
          completion: true
    alibaba:
      toolcalling:
        weather:
          enabled: true
          api-key: ${WEATHER_API_KEY}
        baidu:
          search:
            enabled: true
        tavilysearch:
          api-key: ${TAVILY_API_KEY}
          enabled: true
    dashscope:
      api-key: ${DASHSCOPE_API_KEY}
    openai:
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      api-key: ${DASHSCOPE_API_KEY}
      chat:
        options:
          model: qwen-max-latest
      embedding:
        options:
          model: text-embedding-v1
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      # 应用健康状态检查，携带详细信息
      show-details: always
  tracing:
    sampling:
      # trace 采样信息，记录每个请求
      probability: 1.0
