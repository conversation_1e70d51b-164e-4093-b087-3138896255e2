/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.request;

import java.io.Serializable;
import java.util.Objects;

public class SearchRequest implements Serializable {

	private String query;

	private int topK;

	private String name;

	private String vectorType;

	private String filterFormatted;

	public String getQuery() {
		return query;
	}

	public void setQuery(String query) {
		this.query = query;
	}

	public int getTopK() {
		return topK;
	}

	public void setTopK(int topK) {
		this.topK = topK;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getVectorType() {
		return vectorType;
	}

	public void setVectorType(String vectorType) {
		this.vectorType = vectorType;
	}

	public String getFilterFormatted() {
		return filterFormatted;
	}

	public void setFilterFormatted(String filterFormatted) {
		this.filterFormatted = filterFormatted;
	}

	@Override
	public String toString() {
		return "SearchRequest{" + "query='" + query + '\'' + ", topK=" + topK + ", name='" + name + '\''
				+ ", vectorType='" + vectorType + '\'' + ", filterFormatted='" + filterFormatted + '\'' + '}';
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		SearchRequest that = (SearchRequest) o;
		return topK == that.topK && Objects.equals(query, that.query) && Objects.equals(name, that.name)
				&& Objects.equals(vectorType, that.vectorType) && Objects.equals(filterFormatted, that.filterFormatted);
	}

	@Override
	public int hashCode() {
		return Objects.hash(query, topK, name, vectorType, filterFormatted);
	}

}
