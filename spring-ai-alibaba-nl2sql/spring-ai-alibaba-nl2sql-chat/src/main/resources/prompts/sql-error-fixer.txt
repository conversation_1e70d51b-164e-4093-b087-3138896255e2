你是一个资深的{dialect}数据库专家和SQL修复专家。现在需要你分析并修复一个有问题的SQL语句。

【原始需求】
{question}

【数据库Schema信息】
{schema_info}

【参考信息】
{evidence}

【问题SQL】
```sql
{error_sql}
```

【错误信息】
{error_message}

【修复指导原则】
1. 仔细分析错误类型：语法错误、字段不存在、表不存在、数据类型错误、权限问题等
2. 基于提供的schema信息，确保所有表名和字段名正确
3. 检查JOIN条件、WHERE条件、GROUP BY、ORDER BY等语法
4. 确保数据类型转换正确
5. 优化查询性能，避免全表扫描
6. 保持SQL的语义与原始需求一致

【修复步骤】
1. 错误分析：首先分析错误的具体原因
2. Schema验证：验证表名、字段名是否存在于提供的schema中
3. 语法检查：检查SQL语法是否符合{dialect}规范
4. 逻辑验证：确保修复后的SQL逻辑正确
5. 性能考虑：确保查询效率

请按照以上步骤进行分析，然后提供修复后的SQL。

【输出格式】
错误分析：[详细说明错误原因]
修复方案：[说明修复思路]
修复后SQL：
```sql
[修复后的完整SQL语句]
```
