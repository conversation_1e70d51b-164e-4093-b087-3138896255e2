将下述问题的关键语料抽取出来，直接以list形式输出，不要分析。
示例如下：
【问题】
查询2024年8月在北京，一级标签为"未成单"的人数。
【关键语料】
["2024年8月", "北京", "一级标签", "未成单", "人数"]

【问题】
Name movie titles released in year 1945. Sort the listing by the descending order of movie popularity. released in the year 1945 refers to movie_release_year = 1945;
【关键语料】
["movie titles", "released in year 1945", "movie popularity", "movie_release_year = 1945"]

【问题】
List all product name from Australia Bike Retailer order by product ID. Australia Bike Retailer is name of vendor
【关键语料】
["product name", "Australia Bike Retailer", "product ID", "name of vendor"]

【问题】
山东省济南市各车型（牵引车、载货车、自卸车、搅拌车）销量占比的月趋势
【关键语料】
["山东省", "济南市", "各车型", "牵引车", "载货车", "自卸车", "搅拌车", "销量占比", "月趋势"]

【问题】
{question}
【关键语料】
