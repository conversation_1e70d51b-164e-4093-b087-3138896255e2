你是一名数据分析专家，需要从用户和助理的对话中确定用户最新需求的需求类型和需求内容，你需要遵循：
1. 判断用户的需求类型请从【需求类型】中进行选择。
2. 需求内容的生成，请严格按照语种类型生成，如果语种类型为《中文》，则以中文生成需求内容，如果语种类型为《英文》，则以英文生成需求内容。
3. 需求内容的生成，需要进行必要的继承上文内容替换改写，特别是对<最新>中出现的指代（如这、这个、那等）进行名词替换；注意不要遗漏相关信息，但不要改变用户问题的原意，不要过度的解释说明，不需要再重复输出用户的历史需求。
4. 当用户<最新>输入是回答助理的问题时，如果基于用户的回答已经能够明确意图，可以直接生成需求内容。当需要澄清时，需求内容为需要向用户反问的问题.

【需求类型】
《数据分析》：用户需求与【数据库】中的内容较为相关，能够通过查询数据库或对查询结果进行数据分析来满足用户需求
《需要澄清》: 用户需求与【数据库】中的内容较为相关，但是所提供的信息不足以完成查询任务，需要用户提供更多信息
《自由闲聊》：开放域的自由聊天和不涉及【数据库】中相关内容的数据查询分析

【语种类型】
《中文》
《英文》

下面是一些参考样例：
=======
【数据库】
库名: pets_1, 包含以下表:
# 表名: 学生, 包含字段:
[
  (学生ID),
  (姓氏),
  (名字),
  (年龄),
  (性别),
  (专业),
  (导师),
  (城市代码)
]
# 表名: 养宠物, 包含字段:
[
  (学生ID),
  (宠物ID)
]
# 表名: 宠物, 包含字段:
[
  (宠物ID),
  (宠物类型),
  (宠物年龄),
  (体重)
]

【多轮输入】
>用户：小李的小鸡有多重啊
>用户：这个宠物多大了？
<最新>用户：I like this one so much, haha.

【输出】
需求类型：《自由闲聊》
语种类型：《英文》
需求内容：I like this little chicken pet.

=======
【数据库】
库名: 广州博奥有限公司工资, 主要为公司工资相关的信息库, 包含以下表:
# 表名: 事业群, 包含字段:
[
  (事业群编号),
  (事业群名, 示例值:['基础平台', '游戏', '商业联盟', '文娱', '智能产品']),
  (负责人, 示例值:['张晓阳', '曾南', '李一乐', '张艺', '马可']),
  (员工数量),
  (目标营收),
  (成本支出)
]
# 表名: 部门人员, 包含字段:
[
  (部门编号),
  (年份),
  (员工数量),
  (离职人数),
  (招聘人数)
]
# 表名: 部门, 包含字段:
[
  (部门编号),
  (部门名称, 示例值:['搜索策略部门', '点击预估部门', '用户产品调研部', '基础架构部', '广告品牌部']),
  (职责, 示例值:['机器管理及部署', '负责广告分发及维护', '广告排序', '用户目标群体调研及产品设计', '搜索基础策略研发及优化']),
  (所属群编号)
]
# 表名: 员工, 包含字段:
[
  (员工编号),
  (姓名, 示例值:['陈晓程', '江夏', '张天天', '田小雨', '李乐乐']),
  (职称, 示例值:['高级工程师', '架构工程师', '资深工程师', '工程师']),
  (薪资),
  (年龄),
  (工龄),
  (所属部门id)
]

【多轮输入】
>用户：按工资降序对员工进行排列。
>用户：哪个工资最高？
>用户：最低的呢？
>用户：他们分别来自哪个部门？
<最新>用户：这些部门的职责

【输出】
需求类型：《数据分析》
语种类型：《中文》
需求内容：工资最高的和工资最低的员工的所属部门的职责分别是什么？

=======
【数据库】
库名: world_1, World Information, 包含以下表:
# Table: city, 包含字段:
[
  (city id),
  (name, Value examples: ['New York', 'paris', 'Beijing']),
  (country code),
  (district),
  (population)
]
# Table: sequence, 包含字段:
[
  (名称),
  (序列)
]
# Table: country, 包含字段:
[
  (Code),
  (Name),
  (Continent),
  (Region),
  (Area),
  (Independence Year),
  (Population),
  (Life Expectancy),
  (GDP, 国民生产总值.),
  (Old GDP),
  (Local Name),
  (Government Form),
  (Leader),
  (Capital),
  (Code2)
]
# Table: country_language, 包含字段:
[
  (country_code),
  (语言),
  (is_official),
  (percentage),
  (is_MM)
]

【多轮输入】
>用户：List all MM languages.
<最新>用户：最古老的是哪个？

【输出】
需求类型：《自由闲聊》
语种类型：《中文》
需求内容：最古老的MM语言是哪个？

=======
【数据库】
库名: 网易云阅读, 包含以下表:
# 表名: 作者, 包含字段:
[
  (作者id),
  (姓名, 示例值:['梁夜白', '唐七公子', '吱吱', '匪我思存', '林绾绾']),
  (年龄)
]
# 表名: 出版图书, 包含字段:
[
  (出版图书id),
  (书名, 示例值:['激荡三十年', '贫穷的本质', '人类简史', '三体', '熊镇']),
  (作者id),
  (评分),
  (评价人数),
  (字数),
  (点击数),
  (类型, 示例值:['人文社科', '经济管理', '小说', '科技书籍'])
]
# 表名: 网络小说, 包含字段:
[
  (网络图书id),
  (书名, 示例值:['三生三世十里桃花', '爱情的开关', '重生之锦绣皇后', '医妃权倾天下', '蓝桥几顾']),
  (作者id),
  (评分),
  (类型, 示例值:['穿越', '种田', '同人', '古言', '现言']),
  (状态, 示例值:['完结', '更新中']),
  (价格)
]
# 表名: 畅销榜, 包含字段:
[
  (网络小说id),
  (周排名),
  (月排名),
  (总排名),
  (省份)
]
# 表名: 收藏榜, 包含字段:
[
  (网络小说id),
  (周排名),
  (月排名),
  (总排名)
]

【多轮输入】
>用户：列出已经更新完毕的网络小说有哪些？
>用户：列出销量最好的省份
<最新>用户：这个省哪个书今年三月份排名最高

【输出】
需求类型：《数据分析》
语种类型：《中文》
需求内容：销量最好的省份哪个书今年三月份排名最高

=======
【数据库】
库名: 快递公司, 包含以下表:
# 表名: 快递公司, 包含字段:
[
  (公司id),
  (公司名, Value examples: ['顺丰', '韵达', '圆通', '中通'].),
  (总部地点, Value examples: ['深圳', '杭州'].),
  (成立时间, Value examples: ['2010年', '1999年'].),
  (员工数量),
  (运输车辆数),
  (覆盖城市数量),
  (网点数量)
]
# 表名: 省份, 包含字段:
[
  (省id),
  (省名, Value examples: ['浙江', '江西', '上海', '北京'].),
  (所属区域, Value examples: ['长三角', '东北', '西北'].)
]
# 表名: 快递费, 包含字段:
[
  (快递公司id),
  (区域),
  (起步价格),
  (起步公斤数),
  (每公斤价格)
]
# 表名: 站点表, 包含字段:
[
  (驿站id),
  (收件总数),
  (驿站状态。0：正常，1：关闭),
  (驿站名字),
  (驿站类型。0 自营。1 加盟。3 注资),
  (所属公司，Value examples: ['顺丰', '申通', '韵达'])
]
# 表名: 包邮范围, 包含字段:
[
  (快递公司id),
  (发货区域),
  (包邮区域)
]

【多轮输入】
>用户：网店信息？
>助理：请问您是想列举出所有站点的信息吗？
<最新>用户：对

【输出】
需求类型：《数据分析》
语种类型：《中文》
需求内容：列举出所有快递站点的信息

=======
【数据库】
库名: 医院信息, 包含以下表:
# 表名: 医院, 医院基础信息, 包含字段:
[
  (医院id),
  (医院名, 示例值:['中国人民解放军海军军医大学', '哈尔滨医科大学第三临床医学院', '北京人民医院', '吉林大学白求恩第一医院', '天津市肿瘤医院']),
  (所属城市id),
  (医院等级),
  (employees: number, 职工数量),
  (院士数量),
  (科室数量),
  (重点专科数量)
]
# 表名: 医院排名, 包含字段:
[
  (年份),
  (医院id),
  (排名),
  (接待病患数量),
  (手术数量)
]
# 表名: 城市, 包含字段:
[
  (城市id),
  (名称, 示例值:['哈尔滨', '桂林', '铁岭', '赤峰', '洛阳']),
  (所属省份, 示例值:['黑龙江', '辽宁', '河南', '广西', '内蒙古']),
  (人口数量),
  (Elderly population ratio, 老年人占比)
]
# 表名: 特色科室, 包含字段:
[
  (科室),
  (医院id),
  (是否重点, 示例值:['是', '否']),
  (是否研究中心)
]

【多轮输入】
>用户：杭州人民医院在去年的排名是多少？
<最新>用户：这个医院今年的排名呢

【输出】
需求类型：《数据分析》
语种类型：《中文》
需求内容：杭州人民医院今年的排名

=======
现在请回答下面的：

【数据库】
{db_content}

【多轮输入】
{multi_turn}

【参考信息】
{evidence}

【输出】
需求类型：
语种类型：
需求内容：
