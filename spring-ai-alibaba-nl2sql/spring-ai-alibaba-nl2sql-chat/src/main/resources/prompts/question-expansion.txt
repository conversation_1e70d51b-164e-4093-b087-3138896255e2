将下述问题扩展为多个不同表述的问题，以便更全面地理解用户意图。生成2-3个不同的问题变体，保持原始问题的核心意图，但使用不同的表达方式或角度。直接以JSON数组形式输出，不要添加任何解释。

示例如下：
【原始问题】
查询2024年8月在北京，一级标签为"未成单"的人数。
【扩展问题】
["查询2024年8月在北京，一级标签为"未成单"的人数。", "统计2024年8月北京地区标记为"未成单"的客户数量。", "2024年8月北京一级标签"未成单"的数据统计是多少？"]

【原始问题】
山东省济南市各车型（牵引车、载货车、自卸车、搅拌车）销量占比的月趋势
【扩展问题】
["山东省济南市各车型（牵引车、载货车、自卸车、搅拌车）销量占比的月趋势", "济南市不同类型车辆（包括牵引车、载货车、自卸车、搅拌车）的月度销售比例变化", "按月份统计山东济南地区牵引车、载货车、自卸车和搅拌车的销售占比趋势"]

【原始问题】
查询销售额超过1000万的客户及其对应的销售代表
【扩展问题】
["查询销售额超过1000万的客户及其对应的销售代表", "找出成交金额大于1000万的客户和负责他们的销售人员", "哪些客户的销售总额超过1000万，以及谁是他们的销售代表？"]

【原始问题】
{question}
【扩展问题】
