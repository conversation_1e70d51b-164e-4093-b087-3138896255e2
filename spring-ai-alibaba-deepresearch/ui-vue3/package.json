{"name": "jmanus-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "serve": "vite preview", "dev:server": "mvn spring-boot:run"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2": "^5.1.19", "@iconify/vue": "^4.1.1", "ant-design-vue": "^4.2.6", "ant-design-x-vue": "^1.2.9", "axios": "^1.6.2", "dayjs": "^1.11.10", "highlight.js": "^11.11.1", "katex": "^0.16.22", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-highlightjs": "^4.2.0", "markdown-it-katex": "^2.0.3", "markdown-it-mathjax3": "^4.3.2", "monaco-editor": "^0.45.0", "nprogress": "^0.2.0", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^4.4.0", "uuid": "^11.1.0", "vue": "^3.3.8", "vue-demi": "^0.14.10", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5", "vue3-colorpicker": "^2.3.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.5.1", "@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.6", "@types/lodash": "^4.14.202", "@types/node": "^18.19.3", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^4.5.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.4.0", "cypress": "^13.6.1", "eslint": "^8.54.0", "eslint-plugin-cypress": "^2.15.1", "eslint-plugin-vue": "^9.18.1", "jsdom": "^23.0.1", "less": "^4.3.0", "mockjs": "^1.1.0", "npm-run-all2": "^6.1.1", "prettier": "^3.1.0", "start-server-and-test": "^2.0.3", "typescript": "~5.3.3", "vite": "^5.0.10", "vitest": "^1.0.0", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}