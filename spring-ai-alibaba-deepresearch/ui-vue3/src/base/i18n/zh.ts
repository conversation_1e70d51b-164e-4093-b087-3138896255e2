/*
 * Copyright 2024-2026 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { I18nType } from './type.ts'

const words: I18nType = {
  conversation: '对话',
  plan: '计划执行',
  backHome: '返回首页',
  noPageTip: '您访问的页面不存在。',
  loginDomain: {
    username: '用户名',
    password: '密 码',
    login: '登录',
    title: '用户登录',
  },
  welcome: '你好',
  create_new_conversation: '新的对话',
  help: '帮助',
}

export default words
