# Copyright 2024-2025 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

spring:
  # Redis 配置
  data:
    redis:
      enabled: true
      host: deep-research-redis
  ai:
    alibaba:
      deepresearch:
        rag:
          enabled: true
          # 可以设置为 'simple' 或 'elasticsearch'
          vector-store-type: elasticsearch
          elasticsearch:
            uris: http://deep-research-es:9200
            similarity-function: cosine
