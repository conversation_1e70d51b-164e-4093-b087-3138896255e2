server:
  port: 8080
spring:
  profiles:
    # LangFuse和可观测配置以及专业知识库配置
    include: observability, kb
  application:
    name: spring-ai-alibaba-deepresearch
  # Redis 配置
  data:
    redis:
      enabled: false
      host: localhost
      port: 6379
      password: ${REDIS-PASSWORD}
      timeout: 3000 # 连接超时时间（毫秒）
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}
      base-url: https://dashscope.aliyuncs.com
      embedding:
        options:
          model: text-embedding-v1

    mcp:
      client:
        enabled: false
        type: ASYNC
    alibaba:
      toolcalling:
        baidu:
          search:
            enabled: true
        tavilysearch:
          api-key: ${TAVILY_API_KEY}
          enabled: true
        jinacrawler:
          enabled: false
          api-key: ${JINA_API_KEY}
        serpapi:
          api-key: ${SERPAPI_KEY}
          enabled: true
        aliyunaisearch:
          api-key: ${ALIYUN_AI_SEARCH_API_KEY}
          base-url: ${ALIYUN_AI_SEARCH_BASE_URL}
          enabled: true
      deepresearch:
        # 图执行的最大迭代次数
        max-iterations: 50
        # 定义项目可以使用的搜索引擎
        search-list:
          - tavily
          - aliyun
          - baidu
          - serpapi
        # TODO: 搜索引擎配置
        #  - google_scholar  # 学术
        #  - xiaohongshu     # 生活
        #  - wikipedia       # 百科
        #  - national_statistics  # 数据分析
        #  - google_trends   # 趋势分析
        #  - baidu_index     # 中文趋势分析
        smart-agents:
          enabled: false
          # search-platform-mapping:
          #   academic_research:
          #     primary: google_scholar
          #   lifestyle_travel:
          #     primary: xiaohongshu
          #   encyclopedia:
          #     primary: wikipedia
          #   data_analysis:
          #     primary: national_statistics
          #   general_research:
          #     primary: tavily
        mcp:
          enabled: false
          config-location: classpath:mcp-config.json
        parallel-node-count:
          researcher: 4
          coder: 4
        rag:
          enabled: false
          # 可以设置为 'simple' 或 'elasticsearch'
          vector-store-type: simple
          data:
            # 启动时加载 classpath下data目录中的所有文件
            locations:
              - "classpath:/data/"
        # 报告导出路径配置
        export:
          path: ${AI_DEEPRESEARCH_EXPORT_PATH}
        reflection:
          enabled: true
          max-attempts: 1

logging:
  level:
    com.alibaba.cloud.ai.example.deepresearch: debug

