/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.cloud.ai.example.deepresearch.config.export;

import com.alibaba.cloud.ai.example.deepresearch.service.ExportService;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 导出服务配置类
 *
 * <AUTHOR>
 * @since 2025/6/20
 *
 */
@Configuration
@EnableConfigurationProperties(ExportProperties.class)
public class ExportConfiguration {

	@Bean
	public ExportService exportService(ExportProperties exportProperties) {
		return new ExportService(exportProperties.getPath());
	}

}
