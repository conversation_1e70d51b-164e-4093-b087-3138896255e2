/**
 * Copyright 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Style in CSS Modules.
 */

.app {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.app > header {
  display: flex;
  flex-direction: column;
  align-items: center;
} 

.app > header > img {
  width: 120px;
}

.app > header > p {
  margin: 20px 0;
  text-align: center;
  font-size: 2.6rem;
}

.app > main {
  display: flex;
  flex-direction: column;
  margin: 20px 0 10px;
  font-size: 0.9rem;
}

.link {
  font-size: 1.2rem;
  color: var(--primary);
}

.button {
  outline: none;
  border: none;
  border-radius: 8px;
  padding: 10px 35px;
  background: var(--primary);
  box-shadow: 0 5px 10px 0 #ddd;
  color: white;
  font-size: calc(10px + 2vmin);
}
