{"github": "<PERSON><PERSON><PERSON>", "document": "官方文档", "run": "运行", "traces": "链路", "evaluate": "评估", "chinese": "中文", "english": "英文", "gotoDebug": "去调试", "config": {"notFound": "未找到匹配的结果", "mcpSearch": "搜索MCP服务器...", "mcpConfig": {"title": "MCP 配置管理", "serverList": "MCP服务器列表", "noServers": "暂无MCP服务器配置", "selectServerHint": "请选择左侧的MCP服务器，或点击新建MCP配置", "addSuccess": "添加成功", "addFailed": "添加失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "noServerSelected": "未选择MCP服务器", "jsonEditor": "JSON编辑器", "formatJson": "格式化JSON", "configJsonPlaceholder": "请输入或粘贴MCP配置的JSON内容...", "jsonStatusEmpty": "请输入JSON配置...", "jsonStatusValid": "JSON格式有效", "jsonStatusInvalid": "JSON格式无效", "invalidJson": "无效的JSON格式", "missingMcpServers": "缺少mcpServers字段", "invalidServerConfig": "无效的服务器配置: {serverId}", "invalidArgs": "{serverId} 的 args 字段无效，必须为字符串数组", "invalidArgsType": "{serverId} 的 args[{index}] 必须为字符串", "invalidEnv": "{serverId} 的 env 字段无效，必须为对象或空数组", "invalidEnvType": "{serverId} 的 env[{key}] 必须为字符串", "invalidUrl": "{serverId} 的 url 字段无效", "importSuccess": "导入成功", "importFailed": "导入失败", "importInvalidJson": "导入的JSON无效", "connectionType": "连接类型", "connectionTypePlaceholder": "请选择连接类型", "confirmDelete": "确认删除", "deleteConfirmMessage": "确定要删除这个MCP服务器配置吗？此操作不可恢复。", "deleteWarningText": "删除后无法恢复，请谨慎操作。", "jsonConfigEmpty": "JSON配置不能为空", "jsonFormatError": "JSON格式错误", "jsonConfigSaved": "JSON配置已保存", "argsFormatError": "Args格式错误，请输入有效的JSON数组", "envFormatError": "Env格式错误，请输入有效的JSON对象", "argsStringError": "Args格式错误，每个参数必须是字符串", "envStringError": "Env格式错误，每个值必须是字符串", "updateSuccess": "更新成功", "saveFailed": "保存失败，请重试", "operationFailed": "操作失败", "mcpServerNamePlaceholder": "请输入MCP服务器名称", "enabled": "启用", "disabled": "禁用", "newMcpConfig": "新建MCP配置", "importAll": "全部导入", "exportAll": "全部导出", "command": "Command", "args": "<PERSON><PERSON><PERSON>", "env": "Env", "url": "URL", "save": "保存", "delete": "删除", "reset": "重置", "import": "导入", "cancel": "取消", "exportSuccess": "导出成功", "exportFailed": "导出失败", "statusToggleSuccess": "状态切换成功", "statusToggleFailed": "状态切换失败", "missingUrlField": "缺少url字段: {serverId} - 没有command时必须有url或baseUrl", "urlFieldTip": "💡 需要提供 url 或 baseUrl 字段", "serverConfigWarning": "Server {serverId} has no command but also no url or baseUrl", "jsonSyntaxError": "❌ JSON语法错误 - 请检查括号、逗号、引号等符号是否正确", "jsonIncomplete": "❌ JSON不完整 - 请检查是否缺少结束括号或引号", "jsonNumberError": "❌ JSON数字格式错误 - 请检查数字格式", "jsonStringError": "❌ JSON字符串格式错误 - 请检查引号是否配对", "jsonSyntaxErrorWithMessage": "❌ JSON语法错误: {message}", "correctFormatExample": "💡 正确格式示例: {\"mcpServers\": {\"server-id\": {\"name\": \"服务器名称\", \"url\": \"服务器地址\"}}}", "commandPlaceholder": "例如: uvx", "urlPlaceholder": "例如: https://mcp.example.com/server", "argsPlaceholder": "每行一个参数，例如:\n--from\nmysql_mcp_server_pro\n--mode\nstdio", "envPlaceholder": "键值对格式，每行一个，例如:\nMYSQL_HOST:127.0.0.1\nMYSQL_PORT:3306\nMYSQL_USER:root"}}}