{"github": "<PERSON><PERSON><PERSON>", "document": "Document", "run": "Run", "traces": "Traces", "evaluate": "Evaluate", "chinese": "zh", "english": "en", "gotoDebug": "Debug", "config": {"notFound": "No matching results found", "mcpSearch": "Search MCP servers...", "mcpConfig": {"title": "MCP Configuration Management", "serverList": "MCP Server List", "noServers": "No MCP server configuration", "selectServerHint": "Please select an MCP server on the left or click to create a new MCP configuration", "addSuccess": "Added successfully", "addFailed": "Add failed", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed", "noServerSelected": "No MCP server selected", "jsonEditor": "JSON Editor", "formatJson": "Format JSON", "configJsonPlaceholder": "Please enter or paste the JSON content of the MCP configuration...", "jsonStatusEmpty": "Please enter JSON configuration...", "jsonStatusValid": "JSON format is valid", "jsonStatusInvalid": "JSON format is invalid", "invalidJson": "Invalid JSON format", "missingMcpServers": "Missing mcpS<PERSON><PERSON> field", "invalidServerConfig": "Invalid server config: {serverId}", "invalidArgs": "Invalid args field for {serverId}, must be a string array", "invalidArgsType": "args[{index}] for {serverId} must be a string", "invalidEnv": "Invalid env field for {serverId}, must be an object or empty array", "invalidEnvType": "env[{key}] for {serverId} must be a string", "invalidUrl": "Invalid url field for {serverId}", "importSuccess": "Import successful", "importFailed": "Import failed", "importInvalidJson": "Imported JSON is invalid", "connectionType": "Connection Type", "connectionTypePlaceholder": "Please select connection type", "confirmDelete": "Confirm Delete", "deleteConfirmMessage": "Are you sure you want to delete this MCP server configuration? This action cannot be undone.", "deleteWarningText": "This action cannot be undone. Please proceed with caution.", "jsonConfigEmpty": "JSON configuration cannot be empty", "jsonFormatError": "JSON format error", "jsonConfigSaved": "JSON configuration saved", "argsFormatError": "Args format error, please enter a valid JSON array", "envFormatError": "Env format error, please enter a valid JSON object", "argsStringError": "Args format error, each parameter must be a string", "envStringError": "Env format error, each value must be a string", "updateSuccess": "Updated successfully", "saveFailed": "Save failed, please retry", "operationFailed": "Operation failed", "mcpServerNamePlaceholder": "Please enter MCP server name", "enabled": "Enabled", "disabled": "Disabled", "newMcpConfig": "New MCP Config", "importAll": "Import All", "exportAll": "Export All", "command": "Command", "args": "<PERSON><PERSON><PERSON>", "env": "Env", "url": "URL", "save": "Save", "delete": "Delete", "reset": "Reset", "import": "Import", "cancel": "Cancel", "exportSuccess": "Export successful", "exportFailed": "Export failed", "statusToggleSuccess": "Status toggle successful", "statusToggleFailed": "Status toggle failed", "missingUrlField": "Missing url field: {serverId} - must have url or baseUrl when no command", "urlFieldTip": "💡 Please provide url or baseUrl field", "serverConfigWarning": "Server {serverId} has no command but also no url or baseUrl", "jsonSyntaxError": "❌ JSON syntax error - please check brackets, commas, quotes and other symbols", "jsonIncomplete": "❌ JSON incomplete - please check if ending brackets or quotes are missing", "jsonNumberError": "❌ JSON number format error - please check number format", "jsonStringError": "❌ JSON string format error - please check if quotes are paired", "jsonSyntaxErrorWithMessage": "❌ JSON syntax error: {message}", "correctFormatExample": "💡 Correct format example: {\"mcpServers\": {\"server-id\": {\"name\": \"Server Name\", \"url\": \"Server URL\"}}}", "commandPlaceholder": "e.g.: uvx", "urlPlaceholder": "e.g.: https://mcp.example.com/server", "argsPlaceholder": "One parameter per line, e.g.:\n--from\nmysql_mcp_server_pro\n--mode\nstdio", "envPlaceholder": "Key-value format, one per line, e.g.:\nMYSQL_HOST:127.0.0.1\nMYSQL_PORT:3306\nMYSQL_USER:root"}}}