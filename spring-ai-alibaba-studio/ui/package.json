{"name": "@ice/lite-scaffold", "version": "1.3.0", "description": "A new ice.js project.", "dependencies": {"@ant-design/icons": "^5.5.2", "@ice/runtime": "^1.4.0", "antd": "^5.22.1", "antd-style": "^3.7.1", "i18next-browser-languagedetector": "^8.0.0", "i18next-localstorage-backend": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@applint/spec": "^1.2.3", "@ice/app": "^3.4.0", "@ice/plugin-antd": "^1.0.2", "@ice/plugin-request": "^1.0.3", "@types/node": "^18.11.17", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.35.0", "i18next": "^23.16.5", "react-i18next": "^15.1.1", "stylelint": "^15.2.0", "typescript": "^4.9.5"}, "scripts": {"start": "ice start --speedup", "build": "ice build", "eslint": "eslint ./ --cache --ext .js,.jsx,.ts,.tsx,.mts", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"src/**/*.{css,scss,less}\" --cache", "stylelint:fix": "npm run stylelint -- --fix"}, "publishConfig": {"access": "public"}, "repository": "**************:ice-lab/react-materials.git"}