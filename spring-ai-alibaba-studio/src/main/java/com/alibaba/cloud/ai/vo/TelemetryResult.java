/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.vo;

public class TelemetryResult {

	private String traceId;

	public String getTraceId() {
		return traceId;
	}

	public void setTraceId(String traceId) {
		this.traceId = traceId;
	}

	@Override
	public String toString() {
		return "TelemetryResult{" + "traceId='" + traceId + '\'' + '}';
	}

	public static TelemetryResultBuilder builder() {
		return new TelemetryResultBuilder();
	}

	public static final class TelemetryResultBuilder {

		private String traceId;

		private TelemetryResultBuilder() {
		}

		public static TelemetryResultBuilder aTelemetryResult() {
			return new TelemetryResultBuilder();
		}

		public TelemetryResultBuilder traceId(String traceId) {
			this.traceId = traceId;
			return this;
		}

		public TelemetryResult build() {
			TelemetryResult telemetryResult = new TelemetryResult();
			telemetryResult.setTraceId(traceId);
			return telemetryResult;
		}

	}

}
