/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/*
 * Copyright The OpenTelemetry Authors
 * SPDX-License-Identifier: Apache-2.0
 */

package com.alibaba.cloud.ai.utils;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.io.SegmentedStringWriter;
import java.io.IOException;

public final class JsonUtil {

	public static final JsonFactory JSON_FACTORY = new JsonFactory();

	public static JsonGenerator create(SegmentedStringWriter stringWriter) {
		try {
			return JSON_FACTORY.createGenerator(stringWriter);
		}
		catch (IOException e) {
			throw new IllegalStateException("Unable to create in-memory JsonGenerator, can't happen.", e);
		}
	}

	private JsonUtil() {
	}

}
