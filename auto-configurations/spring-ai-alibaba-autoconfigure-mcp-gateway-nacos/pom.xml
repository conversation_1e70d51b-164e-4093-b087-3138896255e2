<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.alibaba.cloud.ai</groupId>
        <artifactId>spring-ai-alibaba</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>spring-ai-alibaba-autoconfigure-mcp-gateway-nacos</artifactId>

    <name>Spring AI Alibaba Nacos Mcp Gateway Autoconfiguration</name>
    <description>Spring AI Alibaba Nacos Mcp Gateway Autoconfiguration</description>
    <url>https://github.com/alibaba/spring-ai-alibaba</url>
    <scm>
        <connection>git://github.com/alibaba/spring-ai-alibaba.git</connection>
        <developerConnection>**************:alibaba/spring-ai-alibaba.git</developerConnection>
        <url>https://github.com/alibaba/spring-ai-alibaba</url>
    </scm>

    <dependencies>

        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-mcp-gateway-nacos</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-autoconfigure-mcp-gateway-core</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-mcp-server</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>

</project>
