<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2024-2025 the original author or authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.alibaba.cloud.ai</groupId>
        <artifactId>spring-ai-alibaba</artifactId>
        <version>${revision}</version>
        <relativePath>../../../pom.xml</relativePath>
    </parent>

    <artifactId>spring-ai-alibaba-starter-document-reader-gitbook</artifactId>
    <packaging>jar</packaging>
    <name>Spring AI Alibaba Starter Document Reader GitBook</name>
    <description>gitbook reader for Spring AI Alibaba</description>
    <url>https://github.com/alibaba/spring-ai-alibaba</url>
    <scm>
        <connection>git://github.com/alibaba/spring-ai-alibaba.git</connection>
        <developerConnection>**************:alibaba/spring-ai-alibaba.git</developerConnection>
        <url>https://github.com/alibaba/spring-ai-alibaba</url>
    </scm>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven-deploy-plugin.version>3.1.1</maven-deploy-plugin.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!-- test dependencies -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-observation-test</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <repositories>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
            </plugin>
        </plugins>
    </build>

</project>
