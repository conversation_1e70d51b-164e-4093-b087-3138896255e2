/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.prompt;

import org.springframework.ai.chat.prompt.PromptTemplate;

import java.util.List;

/**
 * Builder for configuring a {@link PromptTemplate.Builder}
 *
 * @since 1.0.0.3
 * <AUTHOR>
 */
public class PromptTemplateBuilderConfigure {

	List<PromptTemplateCustomizer> customizers;

	public void setPromptTemplateBuilderCustomizers(List<PromptTemplateCustomizer> customizers) {
		this.customizers = customizers;
	}

	public PromptTemplate.Builder configure(PromptTemplate.Builder promptTemplateBuilder) {
		applyCustomizers(promptTemplateBuilder);
		return promptTemplateBuilder;
	}

	private void applyCustomizers(PromptTemplate.Builder promptTemplateBuilder) {
		if (customizers != null) {
			customizers.forEach(customizer -> customizer.customize(promptTemplateBuilder));
		}
	}

}
